<!-- 关于我们 -->
<template>
	<view class="container">
		<cu-custom bgColor="bg-yellow" :isBack="true">
			<block slot="backText"></block>
			<block slot="content">关于我们</block>
		</cu-custom>
		
		<view class="mainBox">
			<mp-html
									:content="content"
									:show-img-menu="false"
									 
								/>
			 
		</view>
		
		
	</view>
</template>

<script>
	import request from '@/utils/request.js';
	export default {
		data() {
			return {
				content:""
				
			}
		},
	async	onLoad(option) {


		let res = await request.post('index/about', {});
		let obj=JSON.parse(res.data)
		     this.content=obj.about;
			//this.content=
			
		},
		methods: {
			 
		}
	}
</script>

<style lang="scss" scoped>
	.mainBox{
		width: 750rpx;
	}
	.logo{
		width: 140rpx;
		margin: 90rpx auto 30rpx;
		display: block;
		border-radius: 12rpx;
	}
</style>
