<!-- 考试答题 -->
<template>
	<view class="page-container">
		<view class="page-content">
		<view class="floating-button" :style="{ left: buttonLeft + 'px', top: buttonTop + 'px' }" @touchmove="moveButton" @touchstart="touchStart">
			<view class="dot" @click="togglePopup">
				<image src="/static/menu.png" mode="aspectFit" class="dot-icon"></image>
			</view>
			<view class="popup-menu" v-if="showPopup">
				<view class="menu-item" @click="handleJiucuo">纠错</view>


			</view>
		</view>

		<view class="header-section" @touchmove.prevent @touchstart.prevent>
			<cu-custom bgColor="bg-gradual-green" :isBack="true">
				<block slot="backText"><text class="text-xs"></text><text class="text-xs">{{title}}第</text>
					<text class="  text-xl text-bold" style="color:red;margin: 0 2rpx;">{{tisetup}}</text>
					<text class="text-xs">题</text>
					<view class='cu-tag bg-blue sm radius' style="margin: 0 10rpx;"><text class="text-xs">{{type}}</text>
					</view>
				</block>
				<block slot="content">
					<!--
					<view @click="NavCenter" class="action2 text-center" style="width: 140rpx;">
						<text class="margin-right text-green">
							<text class=" cuIcon-roundcheckfill" style="margin-right: 6rpx;"></text>
							<text>{{isRightNum}}</text>
						</text>
						<text class="text-red">
							<text class="cuIcon-roundclosefill" style="margin-right: 6rpx;"></text>
							{{isErrorNum}}
						</text>
					</view>
					-->
				</block>
				<block slot="right">
					<view style="margin: 0 20rpx;">{{score}}分</view>
				</block>

			</cu-custom>
		</view>




		<view class="padding-lr-sm padding-tb-sm">
			<view>


				<view v-for="(item,index) in tidata.data" :key="index">
					<view v-if="index == showListIndex">
						<!-- 标签：单选·第1题 3分 -->



						<view class="margin-top-sm question-content" :style="{ height: questionHeight + 'vh' }">
							<scroll-view scroll-y="true" show-scrollbar="true" class="question-scroll" @touchmove.stop @touchstart.stop>
								<mp-html
									:content="tidata.data[showListIndex].tmcontent"
									:show-img-menu="false"
									@imgtap="handleImageTap"
								/>
								<!-- 题目
								<img style="max-width:100%;height:auto;margin:2px 2px;vertical-align: bottom;"
									:src="BASE_URL+item.tm_p">-->
							</scroll-view>
							<!-- 高度控制拖拽按钮 -->
							<view
								class="height-control-handle"
								@touchstart="handleResizeStart"
								@touchmove="handleResizeMove"
								@touchend="handleResizeEnd"
							>
								<view class="handle-indicator">
									<view class="handle-line"></view>
									<view class="handle-line"></view>
									<view class="handle-line"></view>
								</view>
							</view>
						</view>




						<!-- 单选 · Radio -->
						<view v-if="item.type == '单选'" style="display:flex;width: 100%;">
							<view @click="choose(index_)" v-for="(item,index_) in danxuanop" :key="index_"
								class="margin-tb  " style="align-items: center;   flex:1;  margin-left: 18px;">
								<view :class="tidata.data[showListIndex].select==index_? 'active_y':'' "
									class="sele_num  text-bold">


									<text>{{item.title}}</text>


								</view>

							</view>






						</view>
						<!--
						<view class="padding-tb-xs flex " v-if="item.type == '单选'">
							<button @click="nextSetTimeout()"
								class="cu-btn bg-green margin-lr">下一题</button>
						</view>-->

						<!-- 多选 · Checkbox -->


						<view v-if="item.type == '多选'" style="display: flex;flex-direction: row;">
							<view style="flex:1"
								:class="tidata.data[showListIndex].op[index_].checked ? 'CheckboxActive': ''"
								@click="choose2(index_)" v-for="(item,index_) in tidata.data[showListIndex].op"
								:key="index_" class="margin-tb">
								<view :class="tidata.data[showListIndex].op[index_].checked? 'iconBackground':''"
									class="sele_num  text-bold" style="margin-left: 18px;">
									<text>{{index_==0 ? 'A' : index_==1 ? 'B' : index_==2 ? 'C' : index_==3 ? 'D' : index_==4 ? 'E' : index_==5 ? 'F' : index_==6 ? 'G' : 'H' }}</text>
								</view>

							</view>


						</view>

						<!--
						<view class=""  v-if="item.type == '多选'">

								<button @click="nextSetTimeout()"
									class="cu-btn bg-green margin-lr">下一题</button>
						</view>
-->
												<!-- 解答题 · Upload/Draw -->
						<view class="answer-section" v-if="item.type == '填空'||item.type == '解答'" :class="{ 'draw-mode': answerMode === 'draw' }" :style="{ height: answerSectionHeight }">
							

							<!-- 上传模式 -->
							<view v-if="answerMode === 'upload'" class="margin-tb">
								<view class="cu-bar bg-white">
									<view class="action">
										<text class="cuIcon-titles text-green" style="margin-right: 0;"></text>
										<text class="text-dflg text-bold">上传文件</text>
									</view>
									<view class="action">
										{{imgList.length}}/4
									</view>
								</view>
								<view class="cu-form-group">
									<view class="grid col-4 grid-square flex-sub">
										<view class="bg-img" v-for="(item,index) in imgList" :key="index"
											@tap="ViewImage" :data-url="imgList[index]">
											<image :src="imgList[index]" mode="aspectFill"></image>
											<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
												<text class='cuIcon-close'></text>
											</view>
										</view>
										<view class="solids" @tap="ChooseImage" v-if="imgList.length<4">
											<text class='cuIcon-cameraadd'></text>
										</view>
									</view>
								</view>

							</view>

							<!-- 绘制模式 -->
							<view  class="margin-tb draw-mode-container">
								<!-- 绘制工具栏 -->
								<view class="draw-toolbar">
									<!-- 工具选择 -->
									<view class="tool-group">
										<view class="tool-item" :class="{ active: drawTool === 'pen' }" @tap="setDrawTool('pen')">
											<text class="cuIcon-edit"></text>
											<text class="tool-label">画笔</text>
										</view>
										<view class="tool-item" :class="{ active: drawTool === 'eraser' }" @tap="setDrawTool('eraser')">
											<text class="cuIcon-delete"></text>
											<text class="tool-label">橡皮</text>
										</view>
									</view>

									<!-- 线条粗细/橡皮擦大小 -->
									<view class="tool-group" v-if="drawTool === 'pen'">
										<view class="tool-item" :class="{ active: lineWidth === 2 }" @tap="setLineWidth(2)">
										 
											<text class="tool-label">细</text>
										</view>
										<view class="tool-item" :class="{ active: lineWidth === 5 }" @tap="setLineWidth(5)">
										 
											<text class="tool-label">中</text>
										</view>
										<view class="tool-item" :class="{ active: lineWidth === 8 }" @tap="setLineWidth(8)">
										 
											<text class="tool-label">粗</text>
										</view>
									</view>

									<!-- 橡皮擦大小 -->
									<view class="tool-group" v-if="drawTool === 'eraser'">
										<view class="tool-item" :class="{ active: eraserSize === 15 }" @tap="setEraserSize(15)">
										
											<text class="tool-label">小</text>
										</view>
										<view class="tool-item" :class="{ active: eraserSize === 25 }" @tap="setEraserSize(25)">
											
											<text class="tool-label">中</text>
										</view>
										<view class="tool-item" :class="{ active: eraserSize === 35 }" @tap="setEraserSize(35)">
										
											<text class="tool-label">大</text>
										</view>
									</view>

								 
									<!-- 操作按钮 -->
									<view class="tool-group">
										<view class="tool-item" @tap="clearCanvas">
											<text class="cuIcon-refresh"></text>
											<text class="tool-label">清空</text>
										</view>
										<view class="tool-item" @tap="expandCanvas">
											<text class="cuIcon-add"></text>
											<text class="tool-label">扩展</text>
										</view>
										 
									</view>


									<view class="tool-group">
										<view class="tool-item" @tap="takePhoto">
											<text class="cuIcon-camerafill"></text>
											<text class="tool-label">拍照</text>
										</view>
									</view>
								</view>

								<view class="canvas-container auto-height">
									<!-- 画布显示区域 -->
									<view class="canvas-viewport">
										<canvas
											canvas-id="drawCanvas"
											id="drawCanvas"
											class="drawing-canvas auto-fill"
											:style="{
												height: canvasHeight + 'px',
												marginTop: -canvasScrollOffset + 'px'
											}"
											@touchstart="canvasStart"
											@touchmove="canvasMove"
											@touchend="canvasEnd"
										></canvas>
									</view>

									<!-- 自定义滚动条 -->
									<view class="custom-scrollbar">
										<view
											class="scrollbar-track"
											:style="{ height: scrollbarTrackHeight + 'px' }"
											@touchstart="handleScrollbarTrackTouch"
										>
											<view
												class="scrollbar-thumb"
												:style="{
													height: scrollbarThumbHeight + 'px',
													top: scrollbarThumbTop + 'px'
												}"
												@touchstart="handleScrollbarThumbStart"
												@touchmove="handleScrollbarThumbMove"
												@touchend="handleScrollbarThumbEnd"
											></view>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 可拖动的绘制板 -->
						<view
							v-if="showDrawBoard"
							class="draggable-draw-board"
							:style="{ top: drawBoardTop + 'px', height: (canvasHeight + 100) + 'px' }"
						>
							<!-- 顶部拖拽按钮 -->
							<view
								class="resize-handle resize-handle-top"
								@touchstart="resizeStart($event, 'top')"
								@touchmove="resizeMove"
								@touchend="resizeEnd"
							>
								<view class="resize-indicator">
									<text class="resize-dots">⋯</text>
								</view>
							</view>

							<view
								class="draw-board-header"
								@touchstart="dragStart"
								@touchmove="dragMove"
								@touchend="dragEnd"
							>
								<text class="draw-title">绘制答题板</text>
								<view class="draw-controls">
									<text class="cuIcon-refresh" @tap="clearCanvas"></text>
									<text class="cuIcon-close" @tap="hideDrawingBoard"></text>
								</view>
							</view>

							<canvas
								canvas-id="drawCanvasLarge"
								id="drawCanvasLarge"
								class="large-drawing-canvas"
								:style="{ height: canvasHeight + 'px' }"
								@touchstart="canvasStartLarge"
								@touchmove="canvasMoveLarge"
								@touchend="canvasEndLarge"
							></canvas>

							<!-- 底部拖拽按钮 -->
							<view
								class="resize-handle resize-handle-bottom"
								@touchstart="resizeStart($event, 'bottom')"
								@touchmove="resizeMove"
								@touchend="resizeEnd"
							>
								<view class="resize-indicator">
									<text class="resize-dots">⋯</text>
								</view>
							</view>
						</view>

						<!-- 输入框 · FillBlank/Textarea
						<view v-if="item.type=='填空'">

							<view class="margin-tb">
								<view class="cu-bar bg-white">
									<view class="action">
										<text class="cuIcon-titles text-green" style="margin-right: 0;"></text>
										<text class="text-dflg text-bold">上传文件</text>
									</view>
									<view class="action">
										{{imgList2.length}}/4
									</view>
								</view>
								<view class="cu-form-group">
									<view class="grid col-4 grid-square flex-sub">
										<view class="bg-img" v-for="(item,index) in imgList2" :key="index"
											@tap="ViewImage2" :data-url="imgList2[index]">
											<image :src="imgList2[index]" mode="aspectFill"></image>
											<view class="cu-tag bg-red" @tap.stop="DelImg2" :data-index="index">
												<text class='cuIcon-close'></text>
											</view>
										</view>
										<view class="solids" @tap="ChooseImage2" v-if="imgList2.length<4">
											<text class='cuIcon-cameraadd'></text>
										</view>
									</view>
								</view>
								<view class="padding-top-xs padding-bottom flex justify-center bg-white">
								</view>
							</view>



						</view> -->


					</view>
				</view>


				<view v-if="showAnswer&&(tidata.data[showListIndex].type=='单选'||tidata.data[showListIndex].type=='多选')"
					class="bg-gray padding-sm radius margin-top">

					<text v-if="tidata.data[showListIndex].isRight==0" class="text-red text-bold text-lg">回答错误</text>

					<text v-if="tidata.data[showListIndex].isRight==1" class="text-green text-bold text-lg">回答正确</text>
				</view>

				<view v-if="showAnswer&&(tidata.data[showListIndex].type=='单选'||tidata.data[showListIndex].type=='多选')" class="margin-top-sm answerBox">
					<view class="margin-bottom-xs text-bold">题目解析：</view>
					<view class="margin-bottom-xs">

						<mp-html :content="tidata.data[showListIndex].jxcontent" />
					</view>
				</view>

			</view>
		</view>


	 
		<view class="cu-bar bg-white tabbar border shop" style="display: flex;">
			<view class="action2 text-center" style="">
				<button @click="nextUpload()" class="cu-btn bg-green margin-lr" :disabled="disable" v-if="type=='解答'">确认</button>
				<button @click="choose2next()" v-if="type=='多选'" :disabled="disable" class="cu-btn bg-green margin-lr">确认</button>
				<button @click="ok()" v-if="type=='单选'" :disabled="disable" class="cu-btn bg-green margin-lr">确认</button>

				<button @click="nextText()" v-if="type=='填空'" :disabled="disable" class="cu-btn bg-green margin-lr">确认</button>
			</view>
			<view class="action2 text-center" style="">
			<button :disabled="isDisabled" @click="handleStudy()" class="cu-btn bg-green margin-lr">知识点</button>

<button @click="jj()" class="cu-btn bg-green margin-lr">讲解</button>
			</view>


			<view class="action2 text-center" style="">

				<button @click="nextSetTimeout()" class="cu-btn bg-green margin-lr">下一题</button>


			</view>
		</view>
		<!-- 页面底部状态
		<view class="cu-bar bg-white tabbar border shop">
			<view class="action2 text-center" style="width: 140rpx;">

				<text class="text-sm" @click="nextSetTimeout">下一题</text>
			</view>
			<view @click="NavCenter" class="action2 text-center" style="width: 140rpx;">
				<text class="margin-right text-green">
					<text class=" cuIcon-roundcheckfill" style="margin-right: 6rpx;"></text>
					<text>{{isRightNum}}</text>
				</text>
				<text class="text-red">
					<text class="cuIcon-roundclosefill" style="margin-right: 6rpx;"></text>
					{{isErrorNum}}
				</text>
			</view>
			<view @click="NavCenter" class="action2 text-center" style="width: 140rpx;">
				<text style="font-size: 38rpx;vertical-align: middle;">
					<text class="cuIcon-newshot" style="margin-right: 6rpx;"></text>
				</text>
				<text style="vertical-align: middle;">
					<text class="text-black text-bold">{{showListIndex+1}}</text>
					<text class="text-gray" style="margin: 0 4rpx;">/</text>
					<text class="text-gray">{{tinums}}</text>
				</text>
			</view>
		</view>
 -->


	</view>

	<!-- 拍照弹层 -->
	<view v-if="showPhotoModal" class="photo-modal">
		<view class="modal-mask" @tap="closePhotoModal"></view>
		<view class="modal-content">
			<view class="modal-header">
				<text class="modal-title">拍照上传</text>
				<text class="cuIcon-close" @tap="closePhotoModal"></text>
			</view>

			<!-- 上传模式内容 -->
			<view class="margin-tb"  >
				<view class="cu-bar bg-white">
					<view class="action">
						<text class="cuIcon-titles text-green" style="margin-right: 0;"></text>
						<text class="text-dflg text-bold">上传文件</text>
					</view>
					<view class="action">
						{{imgList.length}}/4
					</view>
				</view>
				<view class="cu-form-group">
					<view class="grid col-4 grid-square flex-sub">
						<view class="bg-img" v-for="(item,index) in imgList" :key="index"
							@tap="ViewImage" :data-url="imgList[index]">
							<image :src="imgList[index]" mode="aspectFill"></image>
							<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids" @tap="ChooseImage" v-if="imgList.length<4 ">
							<text class='cuIcon-cameraadd'></text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 答案弹层 -->
	<view v-if="showAnswerModal" class="answer-modal">
		<view class="modal-mask" @tap="closeAnswerModal"></view>
		<view class="modal-content answer-modal-content">
			<view class="modal-header">
				<text class="modal-title">答案解析</text>
				<text class="cuIcon-close" @tap="closeAnswerModal"></text>
			</view>

			<!-- 答案内容 -->
			<view class="answer-content">
				<!-- 正确性提示 -->
				<view v-if="tidata && tidata.data && tidata.data[showListIndex] && (tidata.data[showListIndex].type=='单选'||tidata.data[showListIndex].type=='多选')"
					class="answer-result">
					<view v-if="tidata.data[showListIndex].isRight==0" class="result-wrong">
						<text class="cuIcon-close"></text>
						<text class="result-text">回答错误</text>
					</view>
					<view v-if="tidata.data[showListIndex].isRight==1" class="result-correct">
						<text class="cuIcon-check"></text>
						<text class="result-text">回答正确</text>
					</view>
				</view>

				<!-- 题目解析 -->
				<view class="answer-analysis">
					<view class="analysis-title">题目解析：</view>
					<view class="analysis-content" v-if="tidata && tidata.data && tidata.data[showListIndex]">
						<mp-html :content="tidata.data[showListIndex].jxcontent" />
					</view>
				</view>

				<!-- 确认按钮 -->
				<view class="answer-actions">
					<button class="cu-btn bg-blue lg" @tap="closeAnswerModal">我知道了</button>
				</view>
			</view>
		</view>
	</view>

</view>
</template>

<script>
	import BASE_URL from '@/api/env.js'
	import request from '@/utils/request';
	import uniIcons from '@/components/uni-icons/uni-icons.vue';
	import testJson from '@/common/testJson.json'
	import Vue from 'vue'
	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				ifjianfen:false,
				zsdflag:false,
				isDisabled:false,
				sql_index: 0,
				notvip:false,
				time: '00:00:00',
				timer: null,
				BASE_URL: BASE_URL,
				title: "练习",
				score: 0,
				cando: true,
				type: "",
				cannext:false,
				tinums: 0,
				disable:false,
				tisetup:1,
				SetTimeout: '',
				danxuanop: [{
					"title": "A"
				}, {
					"title": "B"
				}, {
					"title": "C"
				}, {
					"title": "D"
				}, ],
				duoxuanop: [{
					"title": "A"
				}, {
					"title": "B"
				}, {
					"title": "C"
				}, {
					"title": "D"
				}, ],
				tidata: {},
				list: [

				],
				initscore:50,
				modalName: null,

				showListIndex: 0,
				sList: [],
				userSelect: [], //用户答题记录
				showAnswer: false, //是否错误，展示答案解析

				itemBtnStatus: true, // 答完题后按钮屏蔽，避免重复点击


				imgList: [],
				imgList2: [],
				imguplist:[],
				lessonid: 0,

				textareaInput: '',
				buttonLeft: 5,
				buttonTop: 500,
				startX: 0,
				startY: 0,
				showPopup: false,

				// 答题模式相关
				answerMode: 'draw', // 'upload' | 'draw'

				// 绘制相关
				showDrawBoard: false,
				drawBoardTop: 100,
				dragStartY: 0,
				isDragging: false,

				// 画布大小调整相关
				canvasHeight: 400, // 画布高度
				minCanvasHeight: 300, // 最小高度
				maxCanvasHeight: 600, // 最大高度
				isResizing: false, // 是否正在调整大小
				resizeDirection: null, // 'top' | 'bottom'
				resizeStartY: 0,

				// Canvas绘制相关
				canvasContext: null,
				canvasContextLarge: null,
				isDrawing: false,
				lastX: 0,
				lastY: 0,

				// 高度控制相关
				questionHeight: 30, // 题目区域高度百分比
				isResizingHeight: false, // 是否正在调整高度
				resizeStartY: 0, // 开始拖拽时的Y坐标
				minQuestionHeight: 10, // 最小高度百分比
				maxQuestionHeight: 60, // 最大高度百分比

				// 绘制工具相关
				drawTool: 'pen', // 当前绘制工具：pen(画笔) | eraser(橡皮擦)
				lineWidth: 2, // 线条粗细
				strokeColor: '#000000', // 画笔颜色
				eraserSize: 25, // 橡皮擦大小

				// 画布滚动和扩展相关
				canvasHeight: 500, // 当前画布高度
				canvasScrollOffset: 0, // 画布滚动偏移量
				viewportHeight: 400, // 可视区域高度

				// 自定义滚动条相关
				scrollbarThumbHeight: 100, // 滚动条滑块高度
				scrollbarThumbTop: 0, // 滚动条滑块位置
				scrollbarTrackHeight: 300, // 滚动条轨道高度
				isDraggingScrollbar: false, // 是否正在拖拽滚动条
				scrollbarStartY: 0, // 滚动条拖拽开始位置
				scrollOffsetStart: 0, // 滚动开始时的偏移量

				// 拍照弹层相关
				showPhotoModal: false, // 是否显示拍照弹层

				// 答案弹层相关
				showAnswerModal: false // 是否显示答案弹层

			};
		},
	async	onLoad(options) {
			this.sList = testJson;

			let lesson = options.lesson.split(",")
			if(options.notvip==1){
				this.notvip=true;
			}
		await	this.getdata(lesson[0])
			this.lessonid = lesson[0];
			this.startTimer();
			await	this.cur();

			// 初始化滚动条
			this.$nextTick(() => {
				this.updateScrollbar();
			});
		},
		onReady() {
			// 获取画布容器高度
			const query = uni.createSelectorQuery().in(this);
			query.select('.canvas-viewport').boundingClientRect(data => {
				if (data) {
					this.viewportHeight = data.height;
					this.scrollbarTrackHeight = data.height - 20; // 留出一些边距
					this.updateScrollbar();
				}
			}).exec();

			// 如果当前是绘制模式，初始化画布
			if (this.answerMode === 'draw') {
				setTimeout(() => {
					this.initCanvas();
					this.initLargeCanvas();
				}, 300); // 给DOM更多时间渲染
			}
		},

		onUnload() {
			this.stopTimer();
		},
		computed: {
			isRightNum() {
				return this.userSelect.filter(item => item.isRight === 1).length;
			},
			isErrorNum() {
				return this.userSelect.filter(item => item.isRight === 0).length;
			},
			// 计算答题区域的高度
			answerSectionHeight() {
				if (this.answerMode === 'draw') {
					// 绘制模式下，答题区域占据剩余空间
					const remainingHeight = 100 - this.questionHeight; // 剩余的视口高度百分比
					return `${remainingHeight - 10}vh`; // 减去一些边距
				}
				return 'auto';
			}
		},
		methods: {
			// 拍照按钮点击事件
			takePhoto() {
				this.showPhotoModal = true;
			},

			// 关闭拍照弹层
			closePhotoModal() {
				this.showPhotoModal = false;
			},

			// 关闭答案弹层
			closeAnswerModal() {
				this.showAnswerModal = false;
			},

			// 保存画布图片并上传到服务器
			saveCanvasToServer() {
				if (!this.canvasContext) {
					uni.showToast({
						title: '画布未初始化',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				uni.showLoading({
					title: '正在保存画布...'
				});

				// 将画布内容转换为临时文件
				uni.canvasToTempFilePath({
					canvasId: 'drawCanvas',
					success: (res) => {
						console.log('画布转换为图片成功:', res.tempFilePath);

						// 上传到服务器
						this.uploadCanvasImage(res.tempFilePath);
					},
					fail: (err) => {
						console.error('画布转换为图片失败:', err);
						uni.hideLoading();
						uni.showToast({
							title: '保存失败',
							icon: 'none',
							duration: 2000
						});
					}
				}, this);
			},

			// 上传画布图片到服务器
			uploadCanvasImage(tempFilePath) {
				const that = this;

				uni.uploadFile({
					url: BASE_URL + '/api/app/public/upload', // 服务器上传地址
					filePath: tempFilePath,
					name: 'file', // 文件对应的 key
					formData: {
						'user': 'test'
					},
					success: (uploadRes) => {
						uni.hideLoading();
						console.log('画布图片上传成功:', uploadRes);

						try {
							let jsonobj = JSON.parse(uploadRes.data);
							console.log('上传响应:', jsonobj);

							// 将上传的图片URL添加到图片列表
							that.imguplist.push(jsonobj.url);

							// 也添加到显示列表中
							if (that.imgList.length != 0) {
								that.imgList = that.imgList.concat([tempFilePath]);
							} else {
								that.imgList = [tempFilePath];
							}

							uni.showToast({
								title: '画布保存成功',
								icon: 'success',
								duration: 2000
							});

						} catch (e) {
							console.error('解析上传响应失败:', e);
							uni.showToast({
								title: '保存失败',
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('画布图片上传失败:', err);
						uni.showToast({
							title: '上传失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},

			async jiucuo(){

				uni.navigateTo({
					url: '/pages/other/wtfk?tino='+this.tidata.data[this.showListIndex]['no'],
				});


			},

			async jj(){

				if(this.notvip){
					uni.showToast({
						title: '非会员不能使用讲解功能！',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				uni.showModal({
					title: '确实提示',
					content: '确定要求讲解此题目吗？',
					cancelText: '取消',
					confirmText: '确认',
					success:async res => {
						if (res.confirm) {
							let dotires=	await request.post('ti/jj', {
															"tkid": this.tidata.data[this.showListIndex].id

														});


							uni.showToast({
								title: '操作成功！',

							});
						}
					}
				})


			}
			,

			async getdata(lessonid) {
				console.log(lessonid)
				this.tidata = await request.post('ti/getti', {
					"lessonid": lessonid
				});
                this.tisetup=this.tidata.tisetup;
				console.log(this.tidata);

				if (this.tidata.data == null || this.tidata.data.length == 0) {

					uni.showModal({
						title: '提示',
						content: '无相关题目！',
						success: function(res) {
							uni.redirectTo({
								url: '/pages/index/main'
							})
						}
					});
				}
				this.initscore = this.tidata.score;

				this.score=this.initscore;
				this.tinums = this.tidata.data.length;
				console.log(this.tidata);
				this.type = this.tidata.data[this.showListIndex].type;

				if (this.tidata.state == 0) {
					console.log("即将进行能力评测")
					uni.showToast({
						title: '新章节开启，即将进行能力评测！',

					});

					this.title = "能力评测";
				}

console.log(this.tidata.data[0].tm_p_new);
				// 数据加载完成后，如果有图片数据且画布已初始化，重新加载图片
				if (this.tidata.data[0] && this.tidata.data[0].tm_p_new) {
					console.log('数据加载完成，检查是否需要重新加载图片');
					this.$nextTick(() => {
						setTimeout(() => {
							this.reloadImageToCanvas();
						}, 500); // 给画布初始化一些时间
					});
				}
			},

			startTimer() {
				this.timer = setInterval(() => {
					let parts = this.time.split(':').map(Number);
					let seconds = parts[2];
					let minutes = parts[1];
					let hours = parts[0];

					seconds++;
					if (seconds >= 60) {
						seconds = 0;
						minutes++;
					}
					if (minutes >= 60) {
						minutes = 0;
						hours++;
					}

					this.time = `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(seconds)}`;
				}, 1000);
			},
			stopTimer() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
			pad(num) {
				return num < 10 ? '0' + num : num;
			},


			async ok() {


				let myasn = "";

				if (this.userSelect.length >= this.showListIndex + 1) {
					return
				}
				this.disable=true;
				console.log(2)

				if (this.tidata.data[this.showListIndex]['type'] == '单选') {




					if (this.tidata.data[this.showListIndex].myans == this.tidata.data[this.showListIndex].ans) {

						this.showAnswer = true;


						if (this.tidata.state == 1) {

						 let dotires=	await request.post('ti/doti', {
								"tkid": this.tidata.data[this.showListIndex].id,
								"flag": 1,
								"res": this.tidata.data[this.showListIndex].ans
							});

							this.score=dotires.score;
							console.log(this.score)


						}else{
							let dotires=	await request.post('ti/dotestti', {
															"tkid": this.tidata.data[this.showListIndex].id,
															"flag": 1

														});

							this.score=dotires.score;


						}



						this.tidata.data[this.showListIndex].isRight = 1;

						this.userSelect.push({
							index: this.showListIndex, //题目的下标
							id: this.tidata.data[this.showListIndex].id, //题目id

							select: this.sql_index, //选择的项目下标
							isRight: 1 //是否正确；0错误 1正确
						})
					} else {
						this.tidata.data[this.showListIndex].isRight = 0;
						this.showAnswer = true;

						this.userSelect.push({
							index: this.showListIndex, //题目的下标
							id: this.tidata.data[this.showListIndex].id, //题目id

							select: this.sql_index, //选择的项目下标
							isRight: 0 //是否正确；0错误 1正确
						})

						if (this.tidata.state == 1) {
						let dotires=	await request.post('ti/doti', {
								"tkid": this.tidata.data[this.showListIndex].id,
								"flag": 0,
								"res": this.tidata.data[this.showListIndex].ans
							});

							this.score=dotires.score;
						}else{

							let dotires=	await request.post('ti/dotestti', {
									"tkid": this.tidata.data[this.showListIndex].id,
									"flag": 0
								});

								this.score=dotires.score;

						}
					}



				}




				this.$forceUpdate()


			},
			// 选择答案 - 单选
			async choose(sql_index) {


				if (!this.cando) {
					return;
				}



				let myasn = ''

				this.sql_index = sql_index;


				if (this.sql_index == 0) {
					myasn = "A";

				}
				if (this.sql_index == 1) {
					myasn = "B";

				}
				if (this.sql_index == 2) {
					myasn = "C";

				}
				if (this.sql_index == 3) {
					myasn = "D";

				}
				if (this.sql_index == 4) {
					myasn = "E";

				}
				if (this.sql_index == 5) {
					myasn = "F";

				}





				this.tidata.data[this.showListIndex].myans = myasn;
				this.tidata.data[this.showListIndex].select = sql_index;

				this.$forceUpdate()
				//this.nextSetTimeout()
			},
			// 多选
			choose2(index) {
				// console.log(index)
				if (this.tidata.data[this.showListIndex].op[index].checked) {
					this.$set(this.tidata.data[this.showListIndex].op[index], 'checked', false);
					this.$forceUpdate();
				} else {
					this.$set(this.tidata.data[this.showListIndex].op[index], 'checked', true);
					this.$forceUpdate();
				}
				// console.log(this.sList.data.survey.children[this.showListIndex].children[index])
			},
			async choose2next() {



				let items = this.tidata.data[this.showListIndex].op
				let checkedItems = items.filter(item => item.checked);
				console.log(checkedItems)
				let isRight = 0; //0错误 1正确
				if (checkedItems.length <= 0) { // 未多选
					uni.showToast({
						title: '多选题，请继续选择',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				this.disable=true;
				let myans = checkedItems.map(item => item.title);

				console.log(checkedItems)
				let tmItems = this.tidata.data[this.showListIndex].ans.split(",");
				this.tidata.data[this.showListIndex].myans = myans.join(",");
				console.log(tmItems)
				console.log(tmItems)
				if (tmItems.length != checkedItems.length) { // 选择答案数量与答案数量不符；
					console.log('选择错误')
					isRight = 0;
				} else { // 选择答案数量与答案数量相符，继续判断答案是否正确；
					checkedItems.forEach(item1 => {
						let foundItem = tmItems.find(item2 => item2 === item1.title);
						if (foundItem) {
							// console.log('Found a match:', foundItem);
							isRight = 1;
						} else {
							console.log('No match found for:', item1);
							isRight = 0;
						}
					});
				}

				this.tidata.data[this.showListIndex].isRight = isRight;
				if (this.tidata.state == 1) {
					let dotires=await request.post('ti/doti', {
						"tkid": this.tidata.data[this.showListIndex].id,
						"flag": isRight,
						"res":myans
					});

					this.score=dotires.score;
				}else{

					let dotires=await request.post('ti/dotestti', {
						"tkid": this.tidata.data[this.showListIndex].id,
						"flag": isRight
					});

					this.score=dotires.score;

				}

				// 循环选择的项目，已逗号分隔；
				this.userSelect.push({
					index: this.showListIndex, //题目的下标
					id: this.tidata.data[this.showListIndex].id, //题目id

					select: checkedItems, //选择的项目下标
					isRight: isRight //是否正确；0错误 1正确
				})
				this.itemBtnStatus = false;

				if (isRight) {
					this.showAnswer = true;
				} else {
					this.showAnswer = true;
				}
				this.$forceUpdate()
				//this.nextSetTimeout();
			},
			// 解答题提交
			async nextUpload() {
			 
	if (this.imguplist.length < 1&&!this.canvasContext&& !this.canvasContextLarge) {
					uni.showToast({
						title: '请先拍照上传或绘制答题内容',
						icon: 'none',
						duration: 2000
					})
					return
	}
			if(this.imguplist.length<1){
				this.saveCanvasToServer();
				}
				let that = this;

				// 显示答案弹层而不是直接设置showAnswer
				that.showAnswerModal = true;
				that.showAnswer=true;
				this.disable = true;
				//this.nextSetTimeout();
			},

			// 保存画布为图片
			saveCanvasAsImage() {
				// 优先保存大画布，如果没有则保存小画布
				let canvasId = 'drawCanvas';
				let context = this.canvasContext;

				if (this.showDrawBoard && this.canvasContextLarge) {
					canvasId = 'drawCanvasLarge';
					context = this.canvasContextLarge;
				}

				if (!context) {
					uni.showToast({
						title: '请先进行绘制',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				uni.canvasToTempFilePath({
					canvasId: canvasId,
					success: (res) => {
						// 将绘制的图片添加到图片列表中

						console.log(res);
						if (this.imgList.indexOf(res.tempFilePath) === -1) {
							this.imgList.push(res.tempFilePath);
						}

					 
					},
					fail: (err) => {
						console.error('保存画布失败:', err);
						uni.showToast({
							title: '保存绘制内容失败',
							icon: 'none',
							duration: 2000
						});
					}
				}, this);
			},
			ChooseImage() {
				let that=this;
				console.log(12312312);
				uni.chooseImage({
					count: 4, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
							sourceType: ['camera',"album"],
					success: (res) => {
						console.log("阿斯顿发生")
						   const tempFilePath = res.tempFilePaths[0];

						      // 上传图片
						      uni.uploadFile({
						      	  url: BASE_URL+'/api/app/public/upload', // 服务器上传地址
						        filePath: tempFilePath,
						        name: 'file', // 文件对应的 key
						        formData: {

						          'user': 'test'
						        },
						        success: (uploadRes) => {
									let jsonobj=JSON.parse(uploadRes.data)
									  that.imguplist.push(jsonobj.url)

						        if (that.imgList.length != 0) {
									that.imgList = this.imgList.concat(res.tempFilePaths)
						        } else {
									that.imgList = res.tempFilePaths
						        }
						        },
						        fail: (err) => {
						         uni.showToast({
						         		title: '上传失败',
						         		icon: 'none',
						         		duration: 2000
						         })
						        }
						      });


					}
				});
			},

			ChooseImage2() {

				let that=this;
				uni.chooseImage({
					count: 4, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['camera',"album"],
					success: (res) => {
						 const tempFilePath = res.tempFilePaths[0];

						uni.uploadFile({
						  url: BASE_URL+'/api/app/public/upload', // 服务器上传地址
						  filePath: tempFilePath,
						  name: 'file', // 文件对应的 key
						  formData: {

						  },
						  success: (uploadRes) => {


							 let jsonobj=JSON.parse(uploadRes.data)
							   that.imguplist.push(jsonobj.url)
							   if (that.imgList2.length != 0) {
								  that.imgList2 = this.imgList2.concat(res.tempFilePaths)
							   } else {
								that.imgList2 = res.tempFilePaths
							   }
						  },
						  fail: (err) => {
							 uni.showToast({
								title: '上传失败',
								icon: 'none',
								duration: 2000
							 })
						  }
						});



					}
				});
			},
			ViewImage(e) {
				uni.previewImage({
					urls: this.imgList,
					current: e.currentTarget.dataset.url
				});
			},

			ViewImage2(e) {
				uni.previewImage({
					urls: this.imgList2,
					current: e.currentTarget.dataset.url
				});
			},
			DelImg(e) {
				uni.showModal({
					title: '删除答题',
					content: '确定要删除答题吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							this.imgList.splice(e.currentTarget.dataset.index, 1)
						}
					}
				})
			},

			DelImg2(e) {
				uni.showModal({
					title: '删除答题',
					content: '确定要删除答题吗？',
					cancelText: '取消',
					confirmText: '确认',
					success: res => {
						if (res.confirm) {
							this.imgList2.splice(e.currentTarget.dataset.index, 1)
						}
					}
				})
			},
			//  填空题
			nextText() {



				if (this.imguplist.length < 1  &&this.canvasContext==null && !this.canvasContextLarge==null) {
					uni.showToast({
						title: '请先拍照上传答题',
						icon: 'none',
						duration: 2000
					})
					return
				}

				let that = this;

				if(this.imguplist.length<1){
				this.saveCanvasToServer();
				}

				// 显示答案弹层而不是直接设置showAnswer
				that.showAnswerModal = true;
				that.showAnswer=true;
				this.disable=true;
				this.$forceUpdate()


			},


			jdok(){

				let isRight = 0;
				let that = this;
				this.cannext=true;
				uni.showModal({
					title: '请自评',
					content: '是否正确',
					cancelText: '错误', // 取消按钮名称
					confirmText: '正确',
					success:async function(res) {

						that.imgList=[];
						if (res.confirm) {
							isRight = 1;
						}

						that.tidata.data[that.showListIndex].isRight = isRight;

						if (that.tidata.state == 1) {
						let dotires=	await request.post('ti/doti', {
								"tkid": that.tidata.data[that.showListIndex].id,
								"flag": isRight,
								"pic":that.imguplist.join(",")
							});
						that.imguplist=[];
							console.log("score",dotires)
				          that.score=dotires.score;
						}else{
							let dotires=	await request.post('ti/dotestti', {
									"tkid": that.tidata.data[that.showListIndex].id,
									"flag": isRight
								});

								console.log("score",dotires)
							  that.score=dotires.score;


						}



						that.userSelect.push({
							index: that.showListIndex, //题目的下标
							id: that.tidata.data[that.showListIndex].id, //题目id

							select: that.imgList, //选择的项目下标
							isRight: isRight //是否正确；0错误 1正确 2其他无标准答案题型
						})


						that.nextSetTimeout();
						that.$forceUpdate()
					}
				});







			},

			tkok(){
				console.log(1231231231232);
				let isRight = 0;
				let that = this;
				this.cannext=true;
				uni.showModal({
					title: '请自评',
					content: '是否正确',
					cancelText: '错误', // 取消按钮名称
					confirmText: '正确',
					success:async  function(res) {

						this.imgList2=[];

						if (res.confirm) {
							isRight = 1;
						}
						that.imgList=[];
						that.tidata.data[that.showListIndex].isRight = isRight;
						if (that.tidata.state == 1) {
						let dotires=await	request.post('ti/doti', {
								"tkid": that.tidata.data[that.showListIndex].id,
								"flag": isRight,
								"pic":that.imguplist.join(",")
							});
							console.log("score",dotires)
							that.imguplist=[];
							that.score=dotires.score;


						}else{
							let dotires=await	request.post('ti/dotestti', {
									"tkid": that.tidata.data[that.showListIndex].id,
									"flag": isRight
								});
								console.log("score",dotires)

								that.score=dotires.score;

						}



						that.userSelect.push({
							index: that.showListIndex, //题目的下标
							id: that.tidata.data[that.showListIndex].id, //题目id

							select: that.imgList2, //选择的项目下标
							isRight: isRight //是否正确；0错误 1正确 2其他无标准答案题型
						})

						that.nextSetTimeout();

					}
				});
			},


			async nextSetTimeout() {
				let that=this;
				this.cando = true;

				that.disable=false;

				if(this.state==0){

					let score = this.initscore + this.isRightNum * 3 - this.isErrorNum * 3;

					console.log("=================1")

					if(score<0){
						score=0
					}
					if(score>100){
						score=100
					}
					this.score = score;

				}


				console.log(this.tidata);

				if(!that.ifjianfen&&this.tidata.data[this.showListIndex].type=="填空"&&!this.showAnswer ){
					 uni.showToast({
					 	title: '请答题完成后再进行下一题',
					 	icon: 'none',
					 	duration: 2000
					 })
					 return;
				}

				if (!that.ifjianfen&&this.tidata.data[this.showListIndex].type=="解答"&&!this.showAnswer){
					 uni.showToast({
					 	title: '请答题完成后再进行下一题',
					 	icon: 'none',
					 	duration: 2000
					 })
					 return ;
				}


				if(!that.ifjianfen&&this.tidata.data[this.showListIndex].type=="填空"&&!this.cannext){
					this.tkok();
					this.clearCanvas();
					return;
				}

				 if (!that.ifjianfen&&this.tidata.data[this.showListIndex].type=="解答"&&!this.cannext){
					this.jdok();
					this.clearCanvas();
					console.log(2)
					return;
				}




					console.log(3)
					this.cannext=false;
					if (!that.ifjianfen&&this.userSelect[this.showListIndex] == null) {
						uni.showToast({
							title: '请答题完成后再进行下一题',
							icon: 'none',
							duration: 2000
						})
						return;

					}

					// 答题完成后自动进入下一题
					this.SetTimeout = setTimeout(async () => {


						// if (this.tidata.state == 0) {

						// 	if (this.showListIndex + 1 >= this.tidata.data.length) {

						// 		let score = 50 + this.isRightNum * 3 - this.isErrorNum * 3;
						// 		this.score = score;
						// 		if (score < 40) {
						// 			uni.showModal({
						// 				title: '测试结果',
						// 				content: '本章节测试分数过低，请先学习相关知识点！',
						// 				success: function(res) {
						// 					uni.redirectTo({
						// 						url: '/pages/index/main'
						// 					})
						// 				}
						// 			});

						// 		} else {


						// 			await request.post('ti/updatelessonscore', {
						// 				"lessonid": this.lessonid,
						// 				"score": score
						// 			});

						// 			uni.showModal({
						// 				title: '测试结果',
						// 				content: '评测完成您的得分为：' + score,
						// 				success: function(res) {
						// 					uni.redirectTo({
						// 						url: '/pages/index/main'
						// 					})
						// 				}
						// 			});
						// 		}


						// 	}else{

						// 		this.showListIndex++;

						// 		this.type = this.tidata.data[this.showListIndex].type;
						// 		this.showAnswer = false;
						// 		this.itemBtnStatus = true;
						// 		this.textareaInput = ''
						// 		this.$forceUpdate();
						// 		console.log('下一题，下标：', this.showListIndex)
						// 	}





						// }else{



							this.showAnswer = false;

							this.tidata = await request.post('ti/getti', {
								"lessonid": this.lessonid
							});

							console.log(this.tidata)




							if (this.tidata.data == null || this.tidata.data.length == 0) {
								console.log(this.tidata);

								if(this.tidata.state==0&&this.tidata.tisetup==11){
										console.log(111)
											if (this.tidata.score < 40) {

												await request.post('ti/resetlessonscore', {
													"lessonid": this.lessonid,
													"score": this.tidata.score
												});


												uni.showModal({
													title: '测试结果',
													content: '本章节测试分数过低，请先学习相关知识点！',
													success: function(res) {
														uni.redirectTo({
															url: '/pages/index/main'
														})
													}
												});

											} else {

													console.log(22)
												await request.post('ti/updatelessonscore', {
													"lessonid": this.lessonid,
													"score": this.tidata.score
												});

												uni.showModal({
													title: '测试结果',
													content: '评测完成您的得分为：' + this.tidata.score,
													success: function(res) {
														uni.redirectTo({
															url: '/pages/index/main'
														})
													}
												});
											}


								}else{

									uni.showModal({
										title: '提示',
										content: '无相关题目！请练习其他章节',
										success: function(res) {
											uni.redirectTo({
												url: '/pages/index/main'
											})
										}
									});
								}

							}else{


								that.ifjianfen=false;
								that.disable=false;

								this.tisetup=this.tidata.tisetup;
								this.showListIndex=0;
								this.type = this.tidata.data[this.showListIndex].type;

								this.itemBtnStatus = true;
								this.textareaInput = ''

								// 题目切换时，如果新题目需要画布，重新初始化画布
								if (this.type === '填空' || this.type === '解答') {
									console.log('题目类型切换到需要画布的类型，重新初始化画布');
									this.$nextTick(() => {
										setTimeout(() => {
											this.initCanvas();
											this.initLargeCanvas();
										}, 300);
									});
								}

								this.$forceUpdate();

								this.userSelect=[];
							}



						// }










					await	this.cur()
					}, 500);






			},
			clearTimeout() {
				//清除一次性定时器
				clearTimeout(this.SetTimeout)
			},
			NavCenter() {
				this.modalName = 'bottomModal'
			},
			hideModal(e) {
				this.modalName = null
			},
			goList() {
				uni.navigateTo({
					url: '/pages/answer/list'
				})
			},
			imgF(url) {
				let originalUrl = url;
				let modifiedUrl = originalUrl.replace(/(\/admin-api)/g, 'https://tec.test.minerhaoxue.net/admin-api');
				// let modifiedUrl = originalUrl.replace('/admin-api', 'https://tec.test.minerhaoxue.net/admin-api');




				modifiedUrl = modifiedUrl.replace(/\<img/gi,
					'<img style="max-width:100%;height:auto;margin:2px 2px;vertical-align: bottom;" ');

				return modifiedUrl
			},

			go(index) {

				console.log(index)
			},

			async  cur(){

				await request.post('ti/cur', {
					"lessonid": this.lessonid,
					"curtino": this.tidata.data[this.showListIndex].id
				});




			},

			richTextPreviewImage() {
				// 获取点击的图片地址
				// const src = this.sList.data.survey.children[this.showListIndex].title;
				let conimg = this.sList.data.survey.children[this.showListIndex].title.replace('/admin-api',
					'https://tec.test.minerhaoxue.net/admin-api');
				let imgs = conimg.match(/<img[^>]+>/g); //选择节点中的img
				let imgList = [];

				for (var j = 0; j < imgs.length; j++) {
					imgs[j].replace(/<img[^>]*src=['"]([^'"]+)[^>]*>/gi, function(match, capture) {
						imgList.push(capture)
					})
				}


				uni.previewImage({
					current: imgList,
					urls: imgList,
					current: 0,
					fail: function(res) {
						console.log(res)
					}
				})
			},
			touchStart(e) {
				this.startX = e.touches[0].clientX - this.buttonLeft
				this.startY = e.touches[0].clientY - this.buttonTop
			},

			moveButton(e) {
				const touch = e.touches[0]
				this.buttonLeft = touch.clientX - this.startX
				this.buttonTop = touch.clientY - this.startY
				// Prevent button from moving outside screen
				this.buttonLeft = Math.min(Math.max(0, this.buttonLeft), uni.getSystemInfoSync().windowWidth - 50)
				this.buttonTop = Math.min(Math.max(0, this.buttonTop), uni.getSystemInfoSync().windowHeight - 50)
			},

			togglePopup() {
				this.showPopup = !this.showPopup
			},

			handleJiucuo() {
				this.showPopup = false
				this.jiucuo()
			},

			handleStudy() {
				let that=this;
				that.isDisabled=true;
				this.showPopup = false
				 uni.showModal({
				 	title: '提示',
				 	content: '做题时查看知识点本题判为错，是否继续查看？',
				 	success:async res => {

						console.log(res);
				 		if (res.confirm) {




							that.zsdflag=true;
							that.disable=true;



							if(!that.ifjianfen){
								let dotires=	await request.post('ti/doti', {
										"tkid": that.tidata.data[that.showListIndex].id,
										"flag": 0,

									});


								  that.score=dotires.score;
								that.ifjianfen=true;
							}

							uni.navigateTo({
								url: '/pages/answer/zsd?id=' + this.lessonid
							});


				 			}
				 		 else {
				 			// 用户取消离开，什么都不做，阻止返回
				 		}

						that.isDisabled=false;
				 	}
				 });




			},

			// 答题模式切换
			setAnswerMode(mode) {
				console.log('切换答题模式到:', mode);
				this.answerMode = mode;
				if (mode === 'draw') {
					// 延迟初始化，确保DOM已渲染
					this.$nextTick(() => {
						setTimeout(() => {
							console.log('模式切换后初始化画布');
							this.initCanvas();
							this.initLargeCanvas();
						}, 300); // 增加延迟时间
					});
				}
			},

			// 显示绘制板
			showDrawingBoard() {
				this.showDrawBoard = true;
				this.$nextTick(() => {
					this.initLargeCanvas();
				});
			},

			// 隐藏绘制板
			hideDrawingBoard() {
				this.showDrawBoard = false;
			},

			// 初始化小画布
			initCanvas() {
				console.log('开始初始化画布...');

				// 增加延迟，确保DOM完全渲染
				setTimeout(() => {
					this.$nextTick(() => {
						console.log('尝试创建画布上下文...');

						// 先检查画布元素是否存在
						const query = uni.createSelectorQuery().in(this);
						query.select('#drawCanvas').boundingClientRect(data => {
							console.log('画布元素查询结果:', data);

							if (data) {
								this.canvasContext = uni.createCanvasContext('drawCanvas', this);
								console.log('画布上下文创建结果:', this.canvasContext);

								if (this.canvasContext) {
							console.log('画布上下文创建成功');
							this.updateCanvasStyle();

							// 使用动态高度
							const canvasHeight = this.canvasHeight;

							// 设置白色背景
							this.canvasContext.setFillStyle('#ffffff');
							this.canvasContext.fillRect(0, 0, 700, canvasHeight);

							// 检查是否有图片需要加载
							if (this.tidata && this.tidata.data[0] && this.tidata.data[0].tm_p_new) {
								console.log('发现图片数据，开始加载:', this.tidata.data[0].tm_p_new);
								this.loadImageToCanvas(this.tidata.data[0].tm_p_new);
								
							} else {
								// 只绘制白色背景，不绘制测试点
								this.canvasContext.draw();
							}

							console.log('画布初始化完成，默认颜色:', this.strokeColor);
						} else {
							console.error('画布上下文创建失败，尝试重新初始化...');
							// 重试一次
							setTimeout(() => {
								this.initCanvas();
							}, 500);
						}
					} else {
						console.error('画布元素不存在');
					}
				}).exec();
			});
		}, 200);
	},

	// 加载图片到画布
	loadImageToCanvas(imageUrl) {
		imageUrl=BASE_URL+imageUrl;
		if (!this.canvasContext || !imageUrl) {
			console.log('画布上下文或图片URL不存在');
			return;
		}

		console.log('开始加载图片到画布:', imageUrl);

		// 首先获取原始图片的尺寸信息
		const originalImageUrl = this.tidata && this.tidata.data[0] && this.tidata.data[0].tm_p
			? BASE_URL + this.tidata.data[0].tm_p
			: null;

		if (originalImageUrl) {
			// 获取原始图片信息
			uni.getImageInfo({
				src: originalImageUrl,
				success: (originalRes) => {
					console.log('原始图片信息获取成功:', originalRes);

					// 获取屏幕信息
					uni.getSystemInfo({
						success: (systemInfo) => {
							console.log('系统信息获取成功:', systemInfo);

							// 计算原始图片与屏幕的比例
							const screenWidth = systemInfo.screenWidth;
							const screenHeight = systemInfo.screenHeight;
							const originalImgWidth = originalRes.width;
							const originalImgHeight = originalRes.height;

							// 计算原始图片相对于屏幕的缩放比例
							const screenScaleX = screenWidth / originalImgWidth;
							const screenScaleY = screenHeight / originalImgHeight;
							const screenScale = Math.min(screenScaleX, screenScaleY);

							console.log(`屏幕尺寸: ${screenWidth}x${screenHeight}, 原始图片尺寸: ${originalImgWidth}x${originalImgHeight}, 屏幕缩放比例: ${screenScale}`);

							// 现在获取新图片信息并应用相同的缩放比例
							uni.getImageInfo({
								src: imageUrl,
								success: (res) => {
									console.log('新图片信息获取成功:', res);

									// 计算图片在画布中的位置和大小
									const imgWidth = res.width;
									const imgHeight = res.height;

									// 使用与原始图片相同的屏幕缩放比例
									const drawWidth = imgWidth * screenScale;
									const drawHeight = imgHeight * screenScale;
									const drawX = 0; // 靠左显示，左边距为0
									const drawY = 0; // 从顶部开始

									console.log(`绘制图片: 位置(${drawX}, ${drawY}), 大小(${drawWidth}, ${drawHeight}), 应用屏幕缩放比例: ${screenScale}`);

									// 绘制图片到画布
									this.canvasContext.drawImage(
										imageUrl,
										drawX,
										drawY,
										drawWidth,
										drawHeight
									);

									this.canvasContext.draw();
									console.log('图片加载到画布完成');
								},
								fail: (err) => {
									console.error('新图片加载失败:', err);
									this.canvasContext.draw();
								}
							});
						},
						fail: (err) => {
							console.error('获取系统信息失败:', err);
							// 降级到原来的逻辑
							this.loadImageToCanvasOriginal(imageUrl);
						}
					});
				},
				fail: (err) => {
					console.error('原始图片加载失败:', err);
					// 降级到原来的逻辑
					this.loadImageToCanvasOriginal(imageUrl);
				}
			});
		} else {
			// 没有原始图片，使用原来的逻辑
			this.loadImageToCanvasOriginal(imageUrl);
		}
	},

	// 原始的图片加载逻辑（作为降级方案）
	loadImageToCanvasOriginal(imageUrl) {
		uni.getImageInfo({
			src: imageUrl,
			success: (res) => {
				console.log('图片信息获取成功(降级):', res);

				// 计算图片在画布中的位置和大小
				const canvasWidth = 700;
				const canvasHeight = this.canvasHeight;
				const imgWidth = res.width;
				const imgHeight = res.height;

				// 计算缩放比例，保持图片比例
				const scaleX = canvasWidth / imgWidth;
				const scaleY = canvasHeight / imgHeight;
				const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

				const drawWidth = imgWidth * scale;
				const drawHeight = imgHeight * scale;
				const drawX = 0; // 靠左显示，左边距为0
				const drawY = 0; // 从顶部开始

				console.log(`绘制图片(降级): 位置(${drawX}, ${drawY}), 大小(${drawWidth}, ${drawHeight})`);

				// 绘制图片到画布
				this.canvasContext.drawImage(
					imageUrl,
					drawX,
					drawY,
					drawWidth,
					drawHeight
				);

				this.canvasContext.draw();
				console.log('图片加载到画布完成(降级)');
			},
			fail: (err) => {
				console.error('图片加载失败:', err);
				this.canvasContext.draw();
			}
		});
	},

	// 更新画布样式
	updateCanvasStyle() {
				if (!this.canvasContext) return;

				// 设置默认绘制样式
				if (this.drawTool === 'pen') {
					this.canvasContext.setStrokeStyle(this.strokeColor || '#000000');
					this.canvasContext.setLineWidth(this.lineWidth || 2);
					this.canvasContext.setLineCap('round');
					this.canvasContext.setLineJoin('round');
				}
			},

			// 计算画布可用高度
			calculateCanvasHeight() {
				const systemInfo = uni.getSystemInfoSync();
				const screenHeight = systemInfo.windowHeight;
				const questionHeightPx = (this.questionHeight / 100) * screenHeight;

				// 减去题目区域的padding、控制栏高度等
				const controlBarHeight = 80; // 工具栏高度
				const padding = 60; // 上下padding
				const availableHeight = questionHeightPx - controlBarHeight - padding;

				// 增加最小高度，提供更大的绘画面积
				return Math.max(400, availableHeight);
			},

			// 初始化大画布
			initLargeCanvas() {
				this.$nextTick(() => {
					this.canvasContextLarge = uni.createCanvasContext('drawCanvasLarge', this);
					if (this.canvasContextLarge) {
						this.updateLargeCanvasStyle();
						// 设置白色背景
						this.canvasContextLarge.setFillStyle('#ffffff');
						this.canvasContextLarge.fillRect(0, 0, 700, this.canvasHeight);

						// 检查是否有图片需要加载到大画布
						if (this.tidata && this.tidata.data && this.tidata.data.tm_p_new) {
							console.log('发现图片数据，开始加载到大画布:', this.tidata.data.tm_p_new);
							this.loadImageToLargeCanvas(this.tidata.data.tm_p_new);
						} else {
							this.canvasContextLarge.draw();
						}
					}
				});
			},

		// 加载图片到大画布
		loadImageToLargeCanvas(imageUrl) {
			imageUrl=BASE_URL+imageUrl;console.log(imageUrl)
			if (!this.canvasContextLarge || !imageUrl) {
				console.log('大画布上下文或图片URL不存在');
				return;
			}

			console.log('开始加载图片到大画布:', imageUrl);

			// 首先获取原始图片的尺寸信息
			const originalImageUrl = this.tidata && this.tidata.data[0] && this.tidata.data[0].tm_p
				? BASE_URL + this.tidata.data[0].tm_p
				: null;

			if (originalImageUrl) {
				// 获取原始图片信息
				uni.getImageInfo({
					src: originalImageUrl,
					success: (originalRes) => {
						console.log('大画布原始图片信息获取成功:', originalRes);

						// 获取屏幕信息
						uni.getSystemInfo({
							success: (systemInfo) => {
								console.log('大画布系统信息获取成功:', systemInfo);

								// 计算原始图片与屏幕的比例
								const screenWidth = systemInfo.screenWidth;
								const screenHeight = systemInfo.screenHeight;
								const originalImgWidth = originalRes.width;
								const originalImgHeight = originalRes.height;

								// 计算原始图片相对于屏幕的缩放比例
								const screenScaleX = screenWidth / originalImgWidth;
								const screenScaleY = screenHeight / originalImgHeight;
								const screenScale = Math.min(screenScaleX, screenScaleY);

								console.log(`大画布屏幕尺寸: ${screenWidth}x${screenHeight}, 原始图片尺寸: ${originalImgWidth}x${originalImgHeight}, 屏幕缩放比例: ${screenScale}`);

								// 现在获取新图片信息并应用相同的缩放比例
								uni.getImageInfo({
									src: imageUrl,
									success: (res) => {
										console.log('大画布新图片信息获取成功:', res);

										// 计算图片在画布中的位置和大小
										const imgWidth = res.width;
										const imgHeight = res.height;

										// 使用与原始图片相同的屏幕缩放比例
										const drawWidth = imgWidth * screenScale;
										const drawHeight = imgHeight * screenScale;
										const drawX = 0; // 靠左显示，左边距为0
										const drawY = 0; // 从顶部开始

										console.log(`大画布绘制图片: 位置(${drawX}, ${drawY}), 大小(${drawWidth}, ${drawHeight}), 应用屏幕缩放比例: ${screenScale}`);

										// 绘制图片到大画布
										this.canvasContextLarge.drawImage(
											imageUrl,
											drawX,
											drawY,
											drawWidth,
											drawHeight
										);

										this.canvasContextLarge.draw();
										console.log('图片加载到大画布完成');
									},
									fail: (err) => {
										console.error('大画布新图片加载失败:', err);
										this.canvasContextLarge.draw();
									}
								});
							},
							fail: (err) => {
								console.error('大画布获取系统信息失败:', err);
								// 降级到原来的逻辑
								this.loadImageToLargeCanvasOriginal(imageUrl);
							}
						});
					},
					fail: (err) => {
						console.error('大画布原始图片加载失败:', err);
						// 降级到原来的逻辑
						this.loadImageToLargeCanvasOriginal(imageUrl);
					}
				});
			} else {
				// 没有原始图片，使用原来的逻辑
				this.loadImageToLargeCanvasOriginal(imageUrl);
			}
		},

		// 原始的大画布图片加载逻辑（作为降级方案）
		loadImageToLargeCanvasOriginal(imageUrl) {
			uni.getImageInfo({
				src: imageUrl,
				success: (res) => {
					console.log('大画布图片信息获取成功(降级):', res);

					// 计算图片在画布中的位置和大小
					const canvasWidth = 700;
					const canvasHeight = this.canvasHeight;
					const imgWidth = res.width;
					const imgHeight = res.height;

					// 计算缩放比例，保持图片比例
					const scaleX = canvasWidth / imgWidth;
					const scaleY = canvasHeight / imgHeight;
					const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

					const drawWidth = imgWidth * scale;
					const drawHeight = imgHeight * scale;
					const drawX = 0; // 靠左显示
					const drawY = 0; // 从顶部开始

					console.log(`大画布绘制图片(降级): 位置(${drawX}, ${drawY}), 大小(${drawWidth}, ${drawHeight})`);

					// 绘制图片到大画布
					this.canvasContextLarge.drawImage(
						imageUrl,
						drawX,
						drawY,
						drawWidth,
						drawHeight
					);

					this.canvasContextLarge.draw();
					console.log('图片加载到大画布完成(降级)');
				},
				fail: (err) => {
					console.error('大画布图片加载失败:', err);
					this.canvasContextLarge.draw();
				}
			});
		},

		// 重新加载图片到所有画布
		reloadImageToCanvas() {
			if (this.tidata && this.tidata.data[0] && this.tidata.data[0].tm_p_new) {
				console.log('重新加载图片到所有画布:', this.tidata.data[0].tm_p_new);

				// 重新加载到小画布
				if (this.canvasContext) {
					this.canvasContext.setFillStyle('#ffffff');
					this.canvasContext.fillRect(0, 0, 700, this.canvasHeight);
					this.loadImageToCanvas(this.tidata.data[0].tm_p_new);
				}

				// 重新加载到大画布
				if (this.canvasContextLarge) {
					this.canvasContextLarge.setFillStyle('#ffffff');
					this.canvasContextLarge.fillRect(0, 0, 700, this.canvasHeight);
					this.loadImageToLargeCanvas(this.tidata.data[0].tm_p_new);
				}
			}
		},

			// 更新大画布样式
			updateLargeCanvasStyle() {
				if (!this.canvasContextLarge) return;

				// 设置默认绘制样式
				if (this.drawTool === 'pen') {
					this.canvasContextLarge.setStrokeStyle(this.strokeColor || '#000000');
					this.canvasContextLarge.setLineWidth((this.lineWidth || 2) + 1);
					this.canvasContextLarge.setLineCap('round');
					this.canvasContextLarge.setLineJoin('round');
				}
			},

			// 清除画布
			clearCanvas() {
				if (this.canvasContext) {
					// 使用当前画布高度
					this.canvasContext.clearRect(0, 0, 700, this.canvasHeight);
					this.canvasContext.setFillStyle('#ffffff');
					this.canvasContext.fillRect(0, 0, 700, this.canvasHeight);
					this.canvasContext.draw();
				}
				if (this.canvasContextLarge) {
					this.canvasContextLarge.clearRect(0, 0, 700, this.canvasHeight);
					this.canvasContextLarge.setFillStyle('#ffffff');
					this.canvasContextLarge.fillRect(0, 0, 700, this.canvasHeight);
					this.canvasContextLarge.draw();
				}
			},

			// 小画布绘制事件
			canvasStart(e) {
				// 检查画布上下文是否存在，如果不存在则重新初始化
				if (!this.canvasContext) {
					console.warn('画布上下文不存在，尝试重新初始化...');
					this.initCanvas();
					// 延迟一下再开始绘制
					setTimeout(() => {
						this.canvasStart(e);
					}, 100);
					return;
				}

				this.isDrawing = true;
				// 获取触摸点相对于canvas的坐标
				// 现在使用marginTop来控制显示，所以触摸坐标就是画布坐标
				const touch = e.touches[0];
				this.lastX = touch.x;
				this.lastY = touch.y; // 直接使用触摸坐标

				console.log(`绘制开始: 触摸坐标(${touch.x}, ${touch.y}), 滚动偏移: ${this.canvasScrollOffset}`);
				console.log(`当前绘制工具: ${this.drawTool}, 颜色: ${this.strokeColor}, 线宽: ${this.lineWidth}`);

				// 在开始绘制时设置样式
				if (this.drawTool === 'pen') {
					this.canvasContext.setStrokeStyle(this.strokeColor);
					this.canvasContext.setLineWidth(this.lineWidth);
					this.canvasContext.setLineCap('round');
					this.canvasContext.setLineJoin('round');
					console.log(`画笔样式已设置: 颜色=${this.strokeColor}, 线宽=${this.lineWidth}`);
				} else if (this.drawTool === 'eraser') {
					this.canvasContext.setFillStyle('#ffffff');
					console.log('橡皮擦样式已设置');
				}
			},

			canvasMove(e) {
				if (!this.isDrawing) return;

				// 检查画布上下文是否存在
				if (!this.canvasContext) {
					console.warn('绘制移动时画布上下文不存在');
					return;
				}

				const touch = e.touches[0];
				const currentX = touch.x;
				const currentY = touch.y; // 直接使用触摸坐标

				console.log(`绘制移动: 触摸坐标(${touch.x}, ${touch.y}), 滚动偏移: ${this.canvasScrollOffset}`);
					if (this.drawTool === 'eraser') {
						// 橡皮擦使用白色圆形覆盖
						this.canvasContext.setFillStyle('#ffffff');
						this.canvasContext.beginPath();
						this.canvasContext.arc(currentX, currentY, this.eraserSize / 2, 0, 2 * Math.PI);
						this.canvasContext.fill();
					} else {
						// 画笔使用线条，每次都重新设置样式
						this.canvasContext.setStrokeStyle(this.strokeColor);
						this.canvasContext.setLineWidth(this.lineWidth);
						this.canvasContext.setLineCap('round');
						this.canvasContext.setLineJoin('round');

						this.canvasContext.beginPath();
						this.canvasContext.moveTo(this.lastX, this.lastY);
						this.canvasContext.lineTo(currentX, currentY);
						this.canvasContext.stroke();
					}

					this.canvasContext.draw(true);
					this.lastX = currentX;
					this.lastY = currentY;
				}
			,

			canvasEnd(e) {
				this.isDrawing = false;
			},

			// 大画布绘制事件
			canvasStartLarge(e) {
				this.isDrawing = true;
				const touch = e.touches[0];
				this.lastX = touch.x;
				this.lastY = touch.y;

				// 在开始绘制时设置样式
				if (this.canvasContextLarge) {
					if (this.drawTool === 'pen') {
						this.canvasContextLarge.setStrokeStyle(this.strokeColor);
						this.canvasContextLarge.setLineWidth(this.lineWidth + 1);
						this.canvasContextLarge.setLineCap('round');
						this.canvasContextLarge.setLineJoin('round');
					} else if (this.drawTool === 'eraser') {
						this.canvasContextLarge.setFillStyle('#ffffff');
					}
				}
			},

			canvasMoveLarge(e) {
				if (!this.isDrawing) return;

				const touch = e.touches[0];
				const currentX = touch.x;
				const currentY = touch.y;

				if (this.canvasContextLarge) {
					if (this.drawTool === 'eraser') {
						// 橡皮擦使用白色圆形覆盖
						this.canvasContextLarge.setFillStyle('#ffffff');
						this.canvasContextLarge.beginPath();
						this.canvasContextLarge.arc(currentX, currentY, (this.eraserSize + 5) / 2, 0, 2 * Math.PI);
						this.canvasContextLarge.fill();
					} else {
						// 画笔使用线条，每次都重新设置样式
						this.canvasContextLarge.setStrokeStyle(this.strokeColor);
						this.canvasContextLarge.setLineWidth(this.lineWidth + 1);
						this.canvasContextLarge.setLineCap('round');
						this.canvasContextLarge.setLineJoin('round');

						this.canvasContextLarge.beginPath();
						this.canvasContextLarge.moveTo(this.lastX, this.lastY);
						this.canvasContextLarge.lineTo(currentX, currentY);
						this.canvasContextLarge.stroke();
					}

					this.canvasContextLarge.draw(true);
					this.lastX = currentX;
					this.lastY = currentY;
				}
			},

			canvasEndLarge() {
				this.isDrawing = false;
			},

			// 拖动绘制板（只能通过头部拖动）
			dragStart(e) {
				// 如果正在调整大小，不响应拖动
				if (this.isResizing) return;

				this.isDragging = true;
				const touch = e.touches[0];
				this.dragStartY = touch.clientY - this.drawBoardTop;
			},

			dragMove(e) {
				if (!this.isDragging || this.isResizing) return;

				const touch = e.touches[0];
				const newTop = touch.clientY - this.dragStartY;
				const screenHeight = uni.getSystemInfoSync().windowHeight;
				const boardHeight = this.canvasHeight + 100; // 头部+底部+画布高度
				const maxTop = screenHeight - boardHeight;

				// 限制拖动范围，不能超出屏幕
				this.drawBoardTop = Math.max(50, Math.min(newTop, maxTop));
			},

			dragEnd() {
				this.isDragging = false;
			},

			// 开始调整画布大小
			resizeStart(e, direction) {
				// 阻止事件冒泡，避免触发拖动
				e.stopPropagation();

				this.isResizing = true;
				this.resizeDirection = direction;
				this.resizeStartY = e.touches[0].clientY;
			},

			// 调整画布大小移动
			resizeMove(e) {
				if (!this.isResizing) return;

				const touch = e.touches[0];
				const deltaY = touch.clientY - this.resizeStartY;
				let newHeight = this.canvasHeight;

				if (this.resizeDirection === 'bottom') {
					// 向下拖动增加高度，向上拖动减少高度
					newHeight = this.canvasHeight + deltaY;
				} else if (this.resizeDirection === 'top') {
					// 向上拖动增加高度，向下拖动减少高度
					newHeight = this.canvasHeight - deltaY;
					// 调整绘制板位置，保持底部位置不变
					this.drawBoardTop = this.drawBoardTop + deltaY;
				}

				// 限制高度范围
				newHeight = Math.max(this.minCanvasHeight, Math.min(newHeight, this.maxCanvasHeight));

				// 如果是顶部拖拽，需要重新计算位置
				if (this.resizeDirection === 'top') {
					const heightDiff = newHeight - this.canvasHeight;
					this.drawBoardTop = this.drawBoardTop - heightDiff;
				}

				this.canvasHeight = newHeight;
				this.resizeStartY = touch.clientY;

				// 更新canvas尺寸
				this.updateCanvasSize();
			},

			// 结束调整画布大小
			resizeEnd() {
				this.isResizing = false;
				this.resizeDirection = null;
			},

			// 更新Canvas尺寸
			updateCanvasSize() {
				this.$nextTick(() => {
					if (this.canvasContextLarge) {
						// 重新设置canvas的绘制属性
						this.canvasContextLarge.setStrokeStyle('#000000');
						this.canvasContextLarge.setLineWidth(3);
						this.canvasContextLarge.setLineCap('round');
						this.canvasContextLarge.setLineJoin('round');
						// 设置白色背景
						this.canvasContextLarge.setFillStyle('#ffffff');
						this.canvasContextLarge.fillRect(0, 0, 700, this.canvasHeight);
						this.canvasContextLarge.draw();
					}
				});
			},

			// 处理mp-html中的图片点击事件
			handleImageTap(e) {
				// 阻止默认的保存图片行为
				e.preventDefault && e.preventDefault();
				// 可以在这里添加自定义的图片预览逻辑
				// 例如：uni.previewImage({ urls: [e.detail.src] });
			},

			// 高度控制相关方法
			handleResizeStart(e) {
				this.isResizingHeight = true;
				this.resizeStartY = e.touches[0].clientY;
				// 阻止事件冒泡
				e.stopPropagation();
			},

			handleResizeMove(e) {
				if (!this.isResizingHeight) return;

				// 阻止事件冒泡和默认行为
				e.stopPropagation();
				e.preventDefault();

				const currentY = e.touches[0].clientY;
				const deltaY = currentY - this.resizeStartY;
				const screenHeight = uni.getSystemInfoSync().windowHeight;

				// 计算新的高度百分比
				const heightChange = (deltaY / screenHeight) * 100;
				let newHeight = this.questionHeight + heightChange;

				// 限制高度范围
				newHeight = Math.max(this.minQuestionHeight, Math.min(newHeight, this.maxQuestionHeight));

				this.questionHeight = newHeight;
				this.resizeStartY = currentY;
			},

			handleResizeEnd(e) {
				this.isResizingHeight = false;
				// 阻止事件冒泡
				e.stopPropagation();

				// 高度调整结束后不重新初始化画布，保持绘制内容
				// 移除了重新初始化画布的代码，避免清空已绘制的内容
			},

			// 绘制工具相关方法
			setDrawTool(tool) {
				this.drawTool = tool;
				// 工具切换时立即更新样式
				this.updateCanvasStyle();
				this.updateLargeCanvasStyle();
			},

			setLineWidth(width) {
				this.lineWidth = width;
				// 线条粗细改变时立即更新样式
				this.updateCanvasStyle();
				this.updateLargeCanvasStyle();
			},

			setStrokeColor(color) {
				this.strokeColor = color;
				// 颜色改变时立即更新样式
				this.updateCanvasStyle();
				this.updateLargeCanvasStyle();
			},

			setEraserSize(size) {
				this.eraserSize = size;
				if (this.drawTool === 'eraser') {
					this.$nextTick(() => {
						this.updateCanvasStyle();
						this.updateLargeCanvasStyle();
					});
				}
			},

			// 画布滚动相关方法
			handleCanvasScroll(e) {
				this.scrollTop = e.detail.scrollTop;
				this.isScrolling = true;

				// 延迟重置滚动状态
				clearTimeout(this.scrollTimer);
				this.scrollTimer = setTimeout(() => {
					this.isScrolling = false;
				}, 150);
			},

			// 滚动到底部时扩展画布
			handleScrollToBottom() {
				if (!this.isScrolling) return;

				// 增加画布高度
				this.canvasHeight += 200;

				// 扩展画布背景
				this.$nextTick(() => {
					this.expandCanvasBackground();
				});

				// 显示提示
				uni.showToast({
					title: '画布已扩展',
					icon: 'success',
					duration: 1500
				});
			},

			// 扩展画布背景
			expandCanvasBackground() {
				if (this.canvasContext) {
					// 获取画布的实际宽度
					const canvasWidth = 700; // 或者动态获取
					const oldHeight = this.canvasHeight - 200;

					// 在新增的区域填充白色背景
					this.canvasContext.setFillStyle('#ffffff');
					this.canvasContext.fillRect(0, oldHeight, canvasWidth, 200);
					this.canvasContext.draw(true); // 使用 true 保留之前的绘制内容

					console.log(`画布扩展: 在位置 (0, ${oldHeight}) 添加了 ${canvasWidth}x200 的区域`);
				}
			},

			// 手动扩展画布
			expandCanvas() {
				// 增加画布高度
				this.canvasHeight += 200;

				// 重新计算滚动条
				this.updateScrollbar();

				// 扩展画布背景
				this.$nextTick(() => {
					this.expandCanvasBackground();
				});

				// 显示提示
				uni.showToast({
					title: `画布已扩展`,
					icon: 'success',
					duration: 1500
				});
			},

			// 更新滚动条
			updateScrollbar() {
				// 计算滚动条滑块高度（基于可视区域与总高度的比例）
				const ratio = this.viewportHeight / this.canvasHeight;
				this.scrollbarThumbHeight = Math.max(40, this.scrollbarTrackHeight * ratio); // 增加最小高度

				// 计算滚动条滑块位置
				const maxScrollOffset = Math.max(0, this.canvasHeight - this.viewportHeight);
				if (maxScrollOffset > 0) {
					const scrollRatio = this.canvasScrollOffset / maxScrollOffset;
					this.scrollbarThumbTop = scrollRatio * (this.scrollbarTrackHeight - this.scrollbarThumbHeight);
				} else {
					this.scrollbarThumbTop = 0;
				}

				// 确保滑块位置在有效范围内
				this.scrollbarThumbTop = Math.max(0, Math.min(this.scrollbarThumbTop,
					this.scrollbarTrackHeight - this.scrollbarThumbHeight));
			},

			// 滚动条轨道点击
			handleScrollbarTrackTouch(e) {
				if (this.isDraggingScrollbar) return; // 如果正在拖拽滑块，忽略轨道点击

				const touch = e.touches[0];
				// 使用uni-app的方式获取元素位置
				uni.createSelectorQuery().in(this).select('.scrollbar-track').boundingClientRect(data => {
					if (data) {
						const clickY = touch.clientY - data.top;

						// 计算目标滚动位置
						const scrollRatio = clickY / this.scrollbarTrackHeight;
						const maxScrollOffset = Math.max(0, this.canvasHeight - this.viewportHeight);
						this.canvasScrollOffset = Math.max(0, Math.min(maxScrollOffset, scrollRatio * maxScrollOffset));

						this.updateScrollbar();
					}
				}).exec();
			},

			// 滚动条滑块拖拽开始
			handleScrollbarThumbStart(e) {
				this.isDraggingScrollbar = true;
				this.scrollbarStartY = e.touches[0].clientY;
				this.scrollOffsetStart = this.canvasScrollOffset;
				e.stopPropagation(); // 阻止触发轨道点击
			},

			// 滚动条滑块拖拽移动
			handleScrollbarThumbMove(e) {
				if (!this.isDraggingScrollbar) return;

				const deltaY = e.touches[0].clientY - this.scrollbarStartY;
				const maxScrollOffset = Math.max(0, this.canvasHeight - this.viewportHeight);

				if (maxScrollOffset > 0) {
					// 计算滚动比例
					const thumbMoveRatio = deltaY / (this.scrollbarTrackHeight - this.scrollbarThumbHeight);
					const newScrollOffset = this.scrollOffsetStart + thumbMoveRatio * maxScrollOffset;

					this.canvasScrollOffset = Math.max(0, Math.min(maxScrollOffset, newScrollOffset));
					this.updateScrollbar();
				}
			},

			// 滚动条滑块拖拽结束
			handleScrollbarThumbEnd() {
				this.isDraggingScrollbar = false;
			}
		}
		}
</script>

<style lang="scss" scoped>
	@import "../../components/quill/quill.bubble.css";
	@import "../../components/quill/quill.core.css";
	@import "../../components/quill/quill.snow.css";

	// 页面容器样式 - 禁止手指滚动
	.page-container {
		height: 100vh;
		width: 100%;
		overflow: hidden; // 禁止页面级滚动
		position: fixed;
		top: 0;
		left: 0;

		// 禁用触摸滚动
		touch-action: pan-y; // 允许垂直滚动，但只在指定区域
		-webkit-overflow-scrolling: auto;
	}

	.page-content {
		height: 100vh;
		width: 100%;
		overflow-y: auto; // 允许内容滚动
		overflow-x: hidden;

		// 允许垂直滚动，但禁用水平滚动和缩放
		touch-action: pan-y;
	}

	// 头部区域禁用滚动
	.header-section {
		touch-action: none;
		position: relative;
		z-index: 10000; // 确保头部区域在浮动按钮之上
	}

	.cuIcon-roundclosefill:before {
		content: "\e658";
	}

	.sele_num {
		width: 84rpx;
		height: 84rpx;
		line-height: 1.7;
		font-size: 52rpx;
		text-align: center;
		border-radius: 100%;
		box-shadow: 0 0 5px #999;
		display: inline-block;
		margin-right: 14rpx;
	}

	.sele_num.active_n {
		background-color: #e54d42;
		// line-height: 30rpx;
	}

	.sele_num.active_y {
		background-color: #39b54a;
		// line-height: 30rpx;
	}

	.answerBox {
		font-size: 28rpx;
		color: #8a6d3b;
		background-color: #fcf8e3;
		border: #faebcc solid 1px;
		padding: 20rpx 30rpx;
		border-radius: 8rpx;
		word-break: break-all;
	}

	.cu-tag.sm {
		font-size: 20upx;
		padding: 0upx 12upx;
		height: 40upx;
	}

	.qBox {
		width: 750rpx;
		display: grid;
		justify-content: space-between;
		grid-template-columns: repeat(auto-fill, 70rpx);
		padding: 35rpx 25rpx;
		grid-gap: 20rpx;

		.questionNav {
			width: 58rpx;
			height: 58rpx;
			border-radius: 500rpx;
			text-align: center;
			line-height: 58rpx;
			// border: 2rpx solid #ccc;
			box-shadow: 0 0 5rpx #999;
			font-size: 26rpx;
		}

		.questionNav_y {
			background-color: #39b54a;
			color: #ffffff;
		}

		.questionNav_n {
			background-color: #e54d42;
			color: #ffffff;
		}

		.questionNav_o {
			background-color: #1cbbb4;
			color: #ffffff;
		}
	}

	.iconBackground {
		background-color: #39b54a;
		color: #ffffff;
	}

	.shop {
		position: fixed;
		bottom: 0;
		width: 100%;
	}

	.floating-button {
		position: fixed;
		z-index: 999;

		.dot {
			width: 40px;
			height: 40px;

			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			.dot-icon {
				width: 40px;
				height: 40px;
			}
		}

		.popup-menu {
			position: absolute;
			left: 50px;
			top: 0;
			background: white;
			border-radius: 4px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

			.menu-item {
				padding: 8px 16px;
				font-size: 14px;
				white-space: nowrap;

				&:hover {
					background: #f5f5f5;
				}

				&:not(:last-child) {
					border-bottom: 1px solid #eee;
				}
			}
		}
	}

	// 旋转效果
	.dot-active {
		transform: rotate(180deg);
	}

	// 波纹效果
	.dot {
		position: relative;

		&.dot-active::after {
			content: '';
			position: absolute;
			width: 100%;
			height: 100%;
			border-radius: 50%;
			background: rgba(255, 255, 255, 0.3);
			animation: ripple 0.6s ease-out;
		}
	}

	@keyframes ripple {
		from {
			transform: scale(1);
			opacity: 0.5;
		}
		to {
			transform: scale(1.5);
			opacity: 0;
		}
	}

	// 弹跳效果
	.dot-active {
		animation: bounce 0.5s cubic-bezier(0.36, 0, 0.66, -0.56);
	}

	@keyframes bounce {
		0%, 100% { transform: scale(1); }
		50% { transform: scale(0.8); }
	}

	// 答题选项卡样式 - 缩小版本
	.answer-tabs {
		display: flex;
		background: #f5f5f5;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		padding: 4rpx;

		.tab-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 12rpx 8rpx;
			border-radius: 6rpx;
			background: transparent;
			color: #666;
			font-size: 20rpx;
			transition: all 0.3s ease;

			text {
				margin-bottom: 2rpx;

				&:last-child {
					margin-bottom: 0;
					font-size: 18rpx;
				}
			}

			&.active {
				background: #007AFF;
				color: white;
				transform: translateY(-1rpx);
				box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.3);
			}
		}
	}

	// 绘制工具栏样式
	.draw-toolbar {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		padding: 15rpx;
		background: linear-gradient(135deg, #f8f9fa, #e9ecef);
		border-radius: 12rpx;
		margin-bottom: 10rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		.tool-group {
			display: flex;
			gap: 8rpx;
			margin-left: 10rpx;
			align-items: center;

			&:not(:last-child) {
				border-right: 1rpx solid #dee2e6;
				padding-right: 15rpx;
			}
		}

		.tool-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 8rpx 12rpx;
			border-radius: 8rpx;
			background: #fff;
			margin: 0rpx 3rpx;
			border: 1rpx solid #e9ecef;
			transition: all 0.3s ease;
			min-width: 60rpx;

			text {
				font-size: 24rpx;
				color: #6c757d;
				margin-bottom: 4rpx;

				&:last-child {
					margin-bottom: 0;
				}
			}

			.tool-label {
				font-size: 18rpx !important;
				color: #6c757d;
			}

			&.active {
				background: #007AFF;
				border-color: #007AFF;
				transform: translateY(-2rpx);
				box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);

				text, .tool-label {
					color: #fff !important;
				}
			}

			&:active {
				transform: translateY(0);
			}
		}

		.color-item {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			background: #fff;
			border: 2rpx solid #e9ecef;
			transition: all 0.3s ease;

			.color-dot {
				width: 30rpx;
				height: 30rpx;
				border-radius: 50%;

				&.black { background: #000000; }
				&.red { background: #FF0000; }
				&.blue { background: #0000FF; }
				&.green { background: #00FF00; }
			}

			&.active {
				border-color: #007AFF;
				border-width: 3rpx;
				transform: scale(1.1);
				box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
			}
		}

		.line-preview {
			width: 40rpx;
			height: 20rpx;
			background: #6c757d;
			border-radius: 10rpx;
			margin-bottom: 4rpx;

			&.thin { height: 4rpx; }
			&.medium { height: 8rpx; }
			&.thick { height: 12rpx; }
		}

		.eraser-preview {
			width: 30rpx;
			height: 30rpx;
			background: #f8f9fa;
			border: 2rpx solid #6c757d;
			border-radius: 50%;
			margin-bottom: 4rpx;

			&.small {
				width: 20rpx;
				height: 20rpx;
			}
			&.medium {
				width: 30rpx;
				height: 30rpx;
			}
			&.large {
				width: 40rpx;
				height: 40rpx;
			}
		}

		// 画布信息显示
		.canvas-info {
			display: flex;
			align-items: center;
			padding: 8rpx 12rpx;
			background: rgba(0, 122, 255, 0.1);
			border-radius: 8rpx;
			border: 1rpx solid rgba(0, 122, 255, 0.3);

			.info-label {
				font-size: 20rpx;
				color: #007AFF;
				font-weight: bold;
			}
		}

		// 滚动信息显示
		.scroll-info {
			display: flex;
			align-items: center;
			padding: 8rpx 12rpx;
			background: rgba(255, 152, 0, 0.1);
			border-radius: 8rpx;
			border: 1rpx solid rgba(255, 152, 0, 0.3);

			.info-label {
				font-size: 20rpx;
				color: #FF9800;
				font-weight: bold;
			}
		}
	}

	// 画布容器样式
	.canvas-container {
		background: #fff;
		border: 2rpx solid #e5e5e5;
		border-radius: 8rpx;
		margin: 20rpx 0;
		overflow: hidden;
		position: relative;

		.drawing-canvas {
			width: 100%;
			height: 500rpx; // 增加默认高度
			display: block;
			background: #ffffff;
			touch-action: none; // 防止滚动干扰绘制
		}

		// 自动高度模式
		&.auto-height {
			flex: 1;
			display: flex;
			flex-direction: row; // 水平布局，左侧画布，右侧滚动条
			margin: 10rpx 0;
			min-height: 500rpx; // 增加最小高度
			max-height: 100%; // 限制最大高度
			overflow: visible; // 允许滚动条显示

			// 画布视口
			.canvas-viewport {
				flex: 1;
				overflow: hidden; // 隐藏画布溢出部分，但允许自定义滚动
				position: relative;
				height: 100%; // 确保视口占满容器高度
			}

			// 自定义滚动条
			.custom-scrollbar {
				width: 60rpx; // 增加滚动条宽度
				margin-left: 15rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				touch-action: auto; // 允许滚动条响应触摸事件

				.scrollbar-track {
					width: 24rpx; // 增加轨道宽度
					height: 100%; // 使用100%高度，由父容器控制
					background: #f1f1f1;
					border-radius: 12rpx; // 增加圆角
					position: relative;
					cursor: pointer;
					border: 2rpx solid #e0e0e0; // 添加边框
					touch-action: auto; // 允许轨道响应触摸事件

					.scrollbar-thumb {
						width: 100%;
						background: #007AFF;
						border-radius: 10rpx; // 增加圆角
						position: absolute;
						left: 0;
						cursor: pointer;
						box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3); // 增强阴影
						border: 1rpx solid #0056CC; // 添加边框
						min-height: 40rpx; // 设置最小高度
						touch-action: auto; // 允许滑块响应触摸事件

						&:hover {
							background: #0056CC;
							transform: scale(1.05); // 悬停时稍微放大
						}

						&:active {
							background: #003D99;
							transform: scale(0.95); // 按下时稍微缩小
						}
					}
				}
			}

			.canvas-wrapper {
				width: 100%;
				position: relative;

				.drawing-canvas.auto-fill {
					width: 100%;
					background: #ffffff;
					touch-action: none; // 禁用默认触摸行为，只在画布区域
					display: block;
					// 高度由 :style 动态设置
				}
			}
		}
	}

	// 绘制模式容器
	.draw-mode-container {
		display: flex;
		flex-direction: column;
		height: 100%;

		.draw-toolbar {
			flex-shrink: 0; // 工具栏不缩小
		}

		.canvas-container.auto-height {
			flex: 1; // 占据剩余空间
		}
	}

	// 答题区域样式
	.answer-section {
		&.draw-mode {
			display: flex;
			flex-direction: column;

			// 使用JavaScript动态计算高度，这里设置最小高度
			min-height: 300rpx;
		}
	}



	// 题目内容样式
	.question-content {
		position: relative;
		// height: 30vh; // 动态高度，通过 :style 设置

		.question-scroll {
			height: calc(100% - 30rpx); // 减去拖拽按钮的高度
			padding: 20rpx;
			background: #fff;
			border-radius: 8rpx 8rpx 0 0; // 只有上方圆角
			border: 1rpx solid #e5e5e5;
			border-bottom: none;

			// 允许题目区域的触摸滚动
			touch-action: pan-y;
			-webkit-overflow-scrolling: touch;

			// 题目区域滚动条样式 - 较细，用于区分
			&::-webkit-scrollbar {
				width: 8rpx;
			}

			&::-webkit-scrollbar-track {
				background: #f8f8f8;
				border-radius: 4rpx;
			}

			&::-webkit-scrollbar-thumb {
				background: #007AFF;
				border-radius: 4rpx;
				cursor: pointer;

				&:hover {
					background: #0056CC;
				}

				&:active {
					background: #003D99;
				}
			}
		}
	}

	// 高度控制拖拽按钮样式
	.height-control-handle {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 30rpx;
		background: linear-gradient(135deg, #007AFF, #5856D6);
		border-radius: 0 0 8rpx 8rpx;
		border: 1rpx solid #e5e5e5;
		border-top: none;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: ns-resize;

		// 触摸反馈
		&:active {
			background: linear-gradient(135deg, #0056CC, #4A47B8);
		}

		.handle-indicator {
			display: flex;
			flex-direction: column;
			gap: 2rpx;

			.handle-line {
				width: 40rpx;
				height: 2rpx;
				background: rgba(255, 255, 255, 0.8);
				border-radius: 1rpx;
			}
		}
	}

	// 可拖动绘制板样式
	.draggable-draw-board {
		position: fixed;
		left: 25rpx;
		right: 25rpx;
		width: 700rpx;
		height: 450px;
		background: white;
		border-radius: 16rpx;
		box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);
		z-index: 9999;

		.draw-board-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 30rpx;
			background: linear-gradient(135deg, #007AFF, #5856D6);
			color: white;
			border-radius: 16rpx 16rpx 0 0;
			cursor: move;

			.draw-title {
				font-size: 32rpx;
				font-weight: bold;
			}

			.draw-controls {
				display: flex;
				gap: 30rpx;

				text {
					font-size: 40rpx;
					padding: 10rpx;
					border-radius: 50%;
					background: rgba(255, 255, 255, 0.2);

					&:active {
						background: rgba(255, 255, 255, 0.4);
					}
				}
			}
		}

		.large-drawing-canvas {
			width: 100%;
			height: 400px;
			display: block;
			background: #fafafa;
		}
	}

	// 拍照弹层样式
	.photo-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 10000;
		display: flex;
		align-items: center;
		justify-content: center;

		.modal-mask {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.5);
		}

		.modal-content {
			position: relative;
			width: 90%;
			max-width: 600rpx;
			max-height: 80%;
			background: #fff;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);

			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1rpx solid #e5e5e5;
				background: #f8f9fa;

				.modal-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}

				.cuIcon-close {
					font-size: 40rpx;
					color: #666;
					cursor: pointer;

					&:hover {
						color: #333;
					}
				}
			}

			// 弹层内容区域
			.margin-tb {
				padding: 20rpx;
				max-height: 500rpx;
				overflow-y: auto;
			}
		}
	}

	// 答案弹层样式
	.answer-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 10001; // 比拍照弹层更高
		display: flex;
		align-items: center;
		justify-content: center;

		.modal-mask {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.6);
		}

		.answer-modal-content {
			position: relative;
			width: 95%;
			max-width: 700rpx;
			max-height: 85%;
			background: #fff;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);

			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1rpx solid #e5e5e5;
				background: linear-gradient(135deg, #007AFF, #5856D6);
				color: white;

				.modal-title {
					font-size: 32rpx;
					font-weight: bold;
					color: white;
				}

				.cuIcon-close {
					font-size: 40rpx;
					color: white;
					cursor: pointer;

					&:hover {
						color: #f0f0f0;
					}
				}
			}

			.answer-content {
				padding: 30rpx;
				max-height: 600rpx;
				overflow-y: auto;

				.answer-result {
					margin-bottom: 30rpx;
					padding: 20rpx;
					border-radius: 12rpx;
					text-align: center;

					.result-wrong {
						background: #ffebee;
						color: #d32f2f;
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 10rpx;

						.cuIcon-close {
							font-size: 32rpx;
						}

						.result-text {
							font-size: 28rpx;
							font-weight: bold;
						}
					}

					.result-correct {
						background: #e8f5e8;
						color: #2e7d32;
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 10rpx;

						.cuIcon-check {
							font-size: 32rpx;
						}

						.result-text {
							font-size: 28rpx;
							font-weight: bold;
						}
					}
				}

				.answer-analysis {
					.analysis-title {
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 20rpx;
						padding-bottom: 10rpx;
						border-bottom: 2rpx solid #007AFF;
					}

					.analysis-content {
						font-size: 26rpx;
						line-height: 1.6;
						color: #666;
						margin-bottom: 30rpx;
					}
				}

				.answer-actions {
					text-align: center;
					padding-top: 20rpx;
					border-top: 1rpx solid #e5e5e5;

					.cu-btn {
						width: 200rpx;
						height: 80rpx;
						line-height: 80rpx;
						font-size: 28rpx;
					}
				}
			}
		}
	}
</style>