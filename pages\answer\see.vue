<template>
  <view class="question-list">
    <!-- 列表内容 -->
    <view class="list-container">
      <view class="question-item" v-for="(item, index) in questionList" :key="index">
        <view class="question-info" @click="seeinfo(item.id)">
          <view class="question-no">题号：{{ item.no }}</view>
          <view class="question-type">
            <text>题型：{{ item.type }}</text>
            <text class="divider">|</text>
            <text>自我批阅：<text :class="item.flag == 1 ? 'correct' : 'wrong'">{{ item.flag == 1 ? '正确' : '错误' }}</text></text>
            <text class="divider">|</text>
            <view>老师批阅：<text :class="item.teacherflag == 1 ? 'correct' : 'wrong'">{{ item.teacherflag == 1 ? '正确' : '错误' }}</text></view>
          </view>
        </view>
        
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
		id:0,
      questionList: [],
 
    }
  },
  
  onLoad(options) {
	  this.id=options.id;
    
   
  },
  
  onShow() {
  		 this.loadQuestionList(this.id)
  		 
  		 
  },
  
  methods: {
	  
	  seeinfo(id) {
	    uni.navigateTo({
	      url: `/pages/answer/seeinfo?id=${id}`
	    })
	  },
	  
    async loadQuestionList(id) {
		
		console.log(id);
      try {
        const res = await request.post('index/see')

        console.log(res);
        this.questionList = res.data || []
		
		console.log(res);
      } catch (error) {
        console.error('获取题目列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
	
	 
    
    handleView(id,id2) {
      uni.navigateTo({
        url: `/pages/answer/learninfo?id=${id2}&id2=${id}`
      })
    }
  }
}
</script>

<style lang="scss">
.question-list {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  
  .list-container {
    .question-item {
      background: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .question-info {
        flex: 1;
        
        .question-no {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 16rpx;
        }
        
        .question-type {
          font-size: 28rpx;
          color: #666;
          
          .divider {
            margin: 0 20rpx;
            color: #ddd;
          }
          
          .correct {
            color: #67C23A;
          }
          
          .wrong {
            color: #F56C6C;
          }
        }
      }
      
      .action-btn {
        width: 120rpx;
        height: 60rpx;
        background: #007AFF;
        color: #fff;
        border-radius: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>