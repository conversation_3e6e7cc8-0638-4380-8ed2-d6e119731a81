<template>

		

  <view class="container">

	<cu-custom bgColor="bg-yellow2" :isBack="false" :isLogo="false">
			<block class="text-bold text-lg" slot="content">上课信息</block>
		</cu-custom>

	  
    <!-- 添加固定定位的tab -->
    <view class="tab-fixed">
      <scroll-view 
        class="tab-scroll" 
        scroll-x 
        :scroll-left="scrollLeft"
      >
        <view class="tab-container">
          <view 
            v-for="(item, index) in tabList" 
            :key="index"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @tap="handleTabClick(index)"
          >
            {{ item.lessonname }}
          </view>
        </view>
      </scroll-view>
    </view>


         <!-- 筛选按钮区域 -->
     <view class="filter-container">
       <view class="filter-group">
         <view 
           class="filter-btn first" 
           :class="{ active: statusFilter === 'all' }" 
           @tap="setStatusFilter('all')"
         >
           全部
         </view>
         <view 
           class="filter-btn middle" 
           :class="{ active: statusFilter === 'notStarted' }" 
           @tap="setStatusFilter('notStarted')"
         >
           未开始
         </view>
         <view 
           class="filter-btn last" 
           :class="{ active: statusFilter === 'started' }" 
           @tap="setStatusFilter('started')"
         >
           已开始
         </view>
       </view>
       
       <view class="filter-group">
         <view 
           class="filter-btn first" 
           :class="{ active: typeFilter === 'all' }" 
           @tap="setTypeFilter('all')"
         >
           全部
         </view>
         <view 
           class="filter-btn middle" 
           :class="{ active: typeFilter === 'error' }" 
           @tap="setTypeFilter('error')"
         >
           错题
         </view>
         <view 
           class="filter-btn last" 
           :class="{ active: typeFilter === 'explain' }" 
           @tap="setTypeFilter('explain')"
         >
           讲解
         </view>
       </view>
     </view>


    <!-- 给内容区域添加上边距，防止被固定tab遮挡 -->
    <scroll-view
      class="scroll-container"
      scroll-y
      enable-back-to-top
      enhanced
      :scroll-with-animation="false"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      refresher-threshold="45"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
     
          
        <!-- 课程卡片 -->
        <view class="course-card" v-for="(item, index) in courseList" :key="index">
			
			
          <view class="course-header">
            <view class="left-info">
            
              <text class="course-time">{{ item.d }}</text>
            </view>

            <view class="course-tag">
               <text class="tag-text tag-new"  v-if="item.ifjj==1"
                    >讲解
                
              </text>
              <text class="tag-text tag-hot"   v-if="item.iferror==1"
                    >错题
                
              </text>

               <text class="tag-text tag-tuijian"   v-if="item.ifscore==1"
                    >推荐
                
              </text>

            </view>
            
            <text class="live-status" :class="{
              'on': item.state === '3',
              'off': item.state === '4'
            }">{{ item.state === 2 ? '未开始' : item.state === 3 ? '已开始' : item.state === 4 ? '已上课' : item.state }}</text>
          </view>
          
          <view class="course-content">
            <view class="teacher-info" @tap="goToTeacherDetail(item.teacherid)">
              <image 
                :src="BASE_URL+item.avatar" 
                class="teacher-avatar" 
                mode="aspectFill"
               
              />
              <text class="teacher-name">{{item.name}}</text>
            </view>
            <view class="course-info">
              <view class="course-title" v-html="item.lessonname"></view>
               
            </view>
            <view class="action-button enter"  @tap="goToLearnInfo(item.id)" >
             查看
            </view>
          </view>
        </view>
  

      <!-- 加载状态 -->
      <view class="loading-more" v-if="isLoadingMore">加载中...</view>
      <view class="no-more" v-else-if="!hasMore">没有更多内容了</view>
    </scroll-view>
		<Footer style="z-index: 99999;" :footState="1" :num="messageCount"></Footer>
    <u-popup v-model="qrCodeVisible" mode="center">
			<view class="qr-popup padding-lg">
				<view class="text-center padding-bottom">
					<text class="text-lg text-bold">到期提醒</text>
				</view>
				 
				<view class="text-center padding-top-sm">
					<text class="text-df">有科目会员过期，该科目的部分功能将受到限制，且成绩为暂存状态，成绩参考不准确，为了不影响您的使用，请到公众号充值会员开通</text>
				</view>
			 
			</view>
		</u-popup>
  </view>
  
  
</template>

<script>
import BASE_URL from '@/api/env.js'
import request from '@/utils/request.js';
import Footer from "@/components/footer/footer.vue";
export default {
	components: {
		Footer
	},
  data() {
    return {
      // 添加tab相关数据
      tabList: [
     
      ],
	  messageCount: 0,
	  BASE_URL:"",
      currentTab: 0,
      qrCodeVisible:false,
      scrollLeft: 0,
      lessonid:0,
      // 筛选相关数据
      statusFilter: 'all',  // 状态筛选：all, notStarted, started
      typeFilter: 'all',    // 类型筛选：all, error, explain
      // 原有数据
      courseList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isRefreshing: false,
      isLoadingMore: false
    }
  },
  computed: {
   
  },
async  onLoad() {



  let res3=await request.post('index/lessonscore', {});
	  
	//this.tabList = await request.post('bk/tabs', {});
	this.BASE_URL=BASE_URL;
	 

  this.tabList=res3
  //lessonid 等于 tablist 循环且 enddate ！=“已过期”
  this.tabList.forEach(item => {
    if (item.enddate !== '已过期') {
      this.lessonid = item.lessonid;
    }
  });
	
	// if(this.tabList.length>0){
		
	// 	this.lessonid=this.tabList[0]['id']
	// }


  await request.post('bk/countflag', {});
    await request.post('bk/jisuan', {});
  
	 this.handleTabClick(this.currentTab)
	  
  //  this.loadData();
	
	
	
  },
  methods: {

 goToTeacherDetail(teacherId) { 
  console.log(teacherId);
      uni.navigateTo({
        url: '/pages/answer/intro?id=' + teacherId
      })
    },

    goToLearnInfo(id){
		console.log(id);
		uni.navigateTo({
			url: '/pages/answer/learnlist?id=' + id
		});
	},
    async loadData() {
		let that=this;
		
		
     

      try {
      const res = await this.fetchData()
	  console.log(res)
        if (this.isRefreshing) {
          this.courseList = res.data
      setTimeout(() => {
      	 
       that.isRefreshing = false
       }, 500);
        } else {
      			this.isLoadingMore=false;
          this.courseList = [...this.courseList, ...res.data]
      		  setTimeout(() => {
      		   that.isLoadingMore = false
      		  
      		   }, 500);
        }
        this.hasMore = res.hasMore
        this.page++
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.isLoadingMore = false
        this.isRefreshing = false
      }
    },
    
   async fetchData() {
		// 构建筛选参数
		let params = {
			"lessonid": this.lessonid,
			page: this.page,
			pageSize: this.pageSize
		};
		
		// 添加状态筛选参数
		if (this.statusFilter !== 'all') {
			if (this.statusFilter === 'notStarted') {
				params.state = 1; // 未开始
			} else if (this.statusFilter === 'started') {
				params.state = 2; // 已开始
			}
		}
		
		// 添加类型筛选参数
		if (this.typeFilter !== 'all') {
			if (this.typeFilter === 'error') {
				params.iferror = 1; // 错题
			} else if (this.typeFilter === 'explain') {
				params.ifjj = 1; // 讲解
			}
		}
		
		let res = await request.post('bk/list', params);
		console.log(res);
		let hasmore = true;
		if(res.data.length == 0){
			hasmore = false;
		}
				return {
		    data: res.data,
			hasMore: hasmore
		   }
		
		
		
    //   return new Promise((resolve) => {
    //     setTimeout(() => {
    //       const data = Array(this.pageSize).fill(0).map((_, index) => ({
    //         id: this.page * this.pageSize + index,
         
         
    //         type: ['上课中', '已结束', '未上课'][index % 3],
    //         image: '/static/tou.png',
           
    //         teacher: '测试老师',
    //         time: '11月28日 18:20:20',
    //         action: ['进入学习', '进入学习', '进入学习'][index % 3]
    //       }))
          
		  // console.log(data)
    //       resolve({
    //         data,
    //         hasMore: this.page < 3 // 模拟只有3页数据
    //       })
    //     }, 1000)
    //   })
    },
    
    loadMore() {
      if (!this.hasMore || this.isLoadingMore) return
      this.isLoadingMore = true
      this.loadData()
    },
    
    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadData()
    },
    
    // 状态筛选方法
    setStatusFilter(status) {
      this.statusFilter = status;
      this.filterAndRefresh();
    },
    
    // 类型筛选方法
    setTypeFilter(type) {
      this.typeFilter = type;
      this.filterAndRefresh();
    },
    
    // 筛选后重新加载数据
    filterAndRefresh() {
      this.page = 1;
      this.courseList = [];
      this.hasMore = true;
      this.loadData();
    },
    
    // 添加tab点击处理
    handleTabClick(index) {

      if(this.tabList[index].enddate == '已过期'){
        this.qrCodeVisible  =true
        return;
      }

      this.currentTab = index;
      this.lessonid=this.tabList[index].lessonid;



      
      // 计算滚动位置，使选中的tab居中
      const query = uni.createSelectorQuery().in(this);
      query.select('.tab-item').boundingClientRect(rect => {
        const tabWidth = rect.width;
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        this.scrollLeft = (index * tabWidth) - (screenWidth / 2) + (tabWidth / 2);
      }).exec();
      
      // 这里可以根据tab切换加载不同的数据
      this.page = 1;
      this.courseList = [];
      this.loadData();
    }
  }
}
</script>

<style lang="scss">
	
.zthH {
		// height: var(--status-bar-height);
		// margin-top: 30rpx;
		width: 100%;
	}
	
.container {
  background: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
  .no-more{
	  text-align: center;
  }

	.qr-popup {
		background: #fff;
		width: 600rpx;
		border-radius: 12rpx;
		
		.qr-code {
			width: 400rpx;
			height: 400rpx;
			display: block;
			margin: 0 auto;
		
		}
		}
// 添加固定定位样式
.tab-fixed {
  position: fixed;
  top: calc(var(--status-bar-height) + 70rpx);
  left: 0;
  right: 0;
  z-index: 100;
  height: 120rpx;
  overflow-y: hidden;
  overflow-x: hidden;
  background: #fff;
}

.tab-scroll {
  background: #fff;
  white-space: nowrap;
  height: 120rpx;
  width: 100%;
  
  .tab-container {
    display: inline-flex;
    padding: 0 20rpx;
    
    .tab-item {
      padding: 0 30rpx;
      height: 100rpx;
      line-height: 110rpx;
      font-size: 35rpx;
      color: #333;
      position: relative;
      
      &.active {
        color: #00C8B3;
        font-weight: bold;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: #00C8B3;
          border-radius: 2rpx;
        }
      }
    }
  }
}

// 筛选按钮区域样式
.filter-container {
  position: fixed;
  top: calc(var(--status-bar-height) + 70rpx + 120rpx);
  left: 0;
  right: 0;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
  padding: 20rpx;
  z-index: 99;
  display: flex;
  gap: 40rpx;
  justify-content: center;
  
  .filter-group {
    display: flex;
    background: #fff;
    border-radius: 24rpx;
    border: 1rpx solid #dee2e6;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    .filter-btn {
      padding: 14rpx 28rpx;
      background: transparent;
      border: none;
      text-align: center;
      font-size: 26rpx;
      color: #6c757d;
      transition: all 0.3s ease;
      white-space: nowrap;
      position: relative;
      border-radius: 0;
      
      &.first {
        border-top-left-radius: 23rpx;
        border-bottom-left-radius: 23rpx;
      }
      
      &.middle {
        border-radius: 0;
      }
      
      &.last {
        border-top-right-radius: 23rpx;
        border-bottom-right-radius: 23rpx;
      }
      
      &:not(.last)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        bottom: 20%;
        width: 1rpx;
        background: #dee2e6;
      }
      
      &.active {
        background: linear-gradient(135deg, #00C8B3, #00a699);
        color: #fff;
        font-weight: bold;
        transform: scale(1.02);
        z-index: 1;
        
        &::after {
          display: none;
        }
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// 修改scroll-container样式，为筛选区域留出空间
.scroll-container {
  height: calc(100vh - var(--status-bar-height) - 88rpx - 80rpx - 140rpx - 100rpx);
  width: 100%;
  position: fixed;
  top: calc(var(--status-bar-height) + 88rpx + 80rpx + 140rpx);
  left: 0;
  right: 0;
  bottom: 100rpx;
  -webkit-overflow-scrolling: touch;
}

.date-group {
  margin-bottom: 20rpx;
  
  .date-header {
    padding: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.course-card {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  padding: 20rpx;
  
  .course-header {
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    
    .left-info {
      display: flex;
      align-items: center;
      gap: 10rpx;
      
      .today-tag {
        background: #00C8B3;
        color: #fff;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        font-size: 24rpx;
      }
      
      .course-time {
        color: #666;
        font-size: 26rpx;
      }
    }
    
    // Tag居中显示
    .course-tag {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
      
      .tag-text {
        display: inline-block;
        padding: 6rpx 20rpx;
		margin-left: 10px;
        border-radius: 20rpx;
        font-size: 20rpx;
        font-weight: bold;
        color: #fff;
        text-align: center;
        min-width: 60rpx;
        line-height: 1.2;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        
        // 热门标签样式（错题）
        &.tag-hot {
          background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
          border: 1rpx solid #ff5252;
          animation: hotPulse 2s ease-in-out infinite;
        }
        
        // 新课标签样式（讲解）
        &.tag-new {
          background: linear-gradient(135deg, #4ecdc4, #44a08d);
          border: 1rpx solid #42a5f5;
          animation: newShine 3s ease-in-out infinite;
        }
        
        // 推荐标签样式
        &.tag-tuijian {
          background: linear-gradient(135deg, #28a745, #20c997);
          border: 1rpx solid #28a745;
          animation: recommendPulse 2.5s ease-in-out infinite;
        }
      }
    }
    
    .live-status {
      color: #4080FF;
      font-size: 26rpx;
      margin-left: auto;
	  &.on {
	   
	    color: #00C8B3;
	  }
	  
	  &.off {
	   
	    color: #666;
	  }
    }
  }

  .course-content {
    display: flex;
    align-items: center;
    
    .teacher-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20rpx;
      
      .teacher-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 8rpx;
      }
      
      .teacher-name {
        font-size: 24rpx;
        color: #999;
        white-space: nowrap;
      }
    }
    
    .course-info {
      flex: 1;
      
      .course-title {
        font-size: 30rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .course-subtitle {
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .action-button {
      padding: 12rpx 30rpx;
      border-radius: 30rpx;
      font-size: 28rpx;
      
      &.enter {
        background: #00C8B3;
        color: #fff;
      }
      
      &.register {
        background: #4080FF;
        color: #fff;
      }
    }
  }
}

// 热门标签动画效果（错题）
@keyframes hotPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  }
}

// 新课标签动画效果（讲解）
@keyframes newShine {
  0% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.3);
  }
  33% {
    transform: scale(1.02);
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4);
  }
  66% {
    transform: scale(1.05);
    box-shadow: 0 6rpx 16rpx rgba(78, 205, 196, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.3);
  }
}

// 推荐标签动画效果
@keyframes recommendPulse {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
  }
  25% {
    transform: scale(1.02) rotate(1deg);
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.4);
  }
  50% {
    transform: scale(1.04) rotate(0deg);
    box-shadow: 0 6rpx 16rpx rgba(40, 167, 69, 0.5);
  }
  75% {
    transform: scale(1.02) rotate(-1deg);
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.4);
  }
  100% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
  }
}
</style>