<template>
    <view class="m-footer">
        <image src="../../static/icon/footer_bg.png" class="m-footer-bg" mode="widthFix"></image>
        <view class="flex-box flex-col-end">
            <navigator v-for="(item,index) in (list)" :key="item.index" hover-class="none" class="m-footer-item"
                open-type="redirect" :url="item.pagePath" :data-index="index">
                <image class="foot-icon" :src="footState === index ? item.selectedIconPath : item.iconPath" :class="index == 2?'none':''"></image>
                <view class="foot-text" :style="'color: '+(footState === index ? selectedColor : color)">
                    {{item.text}}
                </view>
                <text class="foot-badge" v-if="index == 1 && num>0">{{num}}</text>
            </navigator>
        </view>
        <view class="footer_iphonex"></view>
    </view>
</template>

<script>
    export default {
        name: "Footer",
        props: {
            footState: {
                default: 0,
                type: Number
            },
            num: {
                default: 0,
                type: [Number, Boolean]
            }
        },
        data() {
            return {
                color: "#555555",
                selectedColor: "#f4b400",
                list: [{
                        "pagePath": "/pages/index/index",
                        "text": "首页",
                        "iconPath": "../../static/icon/footer1.png",
                        "selectedIconPath": "../../static/icon/footer1-on.png"
                    },
                    {
                        "pagePath": "/pages/answer/learn",
                        "text": "上课",
                        "iconPath": "../../static/icon/footer2.png",
                        "selectedIconPath": "../../static/icon/footer2-on.png"
                    },
                    {
                        "pagePath": "/pages/index/main",
                        "text": "练习",
                        "iconPath": "../../static/icon/footer1.png",
                        "selectedIconPath": "../../static/icon/footer1-on.png"
                    },
                    {
                        "pagePath": "/pages/index/page3",
                        "text": "交流学习",
                        "iconPath": "../../static/icon/footer4.png",
                        "selectedIconPath": "../../static/icon/footer4-on.png"
                    },
                    {
                        "pagePath": "/pages/index/page4",
                        "text": "个人中心",
                        "iconPath": "../../static/icon/footer5.png",
                        "selectedIconPath": "../../static/icon/footer5-on.png"
                    }
                ],
            };
        },
    }
</script>
<style scoped>
    /* components/u-foot/index.css */
    .m-footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999999;
        width: 100%;
        box-sizing: border-box;
    }
    .m-footer-item {
        -webkit-flex: 1;
        flex: 1;
        position: relative;
        padding: 0 0 6rpx;
    }

    .m-footer-item .foot-icon {
        display: block;
        margin: 0 auto 10rpx;
        width: 45rpx;
        height: 45rpx;
    }
    .foot-icon.none{
        opacity: 0;
        width: 100rpx;
        height: 100rpx;
    }

    .m-footer-item .foot-text {
        text-align: center;
        line-height: 24rpx;
        font-size: 24rpx;
        font-weight: 400;
    }

    .m-footer-item .foot-badge {
        position: absolute;
        right: 30rpx;
        top: -10rpx;
        box-sizing: border-box;
        min-width: 28rpx;
        padding: 0 6rpx;
        height: 28rpx;
        line-height: 28rpx;
        border-radius: 100rpx;
        color: #fff;
        font-size: 22rpx;
        font-weight: 500;
        text-align: center;
        background-color: #f44;
    }

    .m-footer-bg {
        display: block;
        width: 750rpx;
        height: 146rpx;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .footer_iphonex {
        width: 750rpx;
        height: 0;
        background-color: var(--white);
    }

    @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
        .footer_iphonex {
            padding-bottom: 34px;
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
        }

        .m-footer-bg {
            bottom: 34px;
            bottom: constant(safe-area-inset-bottom);
            bottom: env(safe-area-inset-bottom);
        }
    }
</style>
