<template>

  <view class="list-container">
    <scroll-view
      class="scroll-view"
      scroll-y
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view @click="show(item)"
        class="list-item" 
        v-for="(item, index) in listData" 
        :key="index"
      >
        <view class="item-content">
          <view class="item-title">
            <text>{{ item.name }}</text>
            
          </view>
          <view class="item-info">
            <text> {{ item.remarks }}</text>
           
          </view>
        </view>
      </view>
      <view class="loading-more" v-if="isLoadingMore">
        <text>加载中...</text>
      </view>
      <view class="no-more" v-else-if="!hasMore">
        <text>没有更多内容了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
	import request from '@/utils/request.js';
export default {
  data() {
    return {
      listData: [],
      page: 1,
      pageSize: 10,
	  lessonid:0,
      hasMore: true,
      isRefreshing: false,
      isLoadingMore: false
    }
  },
  onLoad(options) {
	  this.lessonid=options.id;
    this.loadData()
  },
  methods: {
	  
	  show(item){
		  console.log(item)
		  
		  uni.navigateTo({
		  	url: '/pages/answer/zsdinfo?id=' + item.id
		  })
		  
	  }
	  ,
    async loadData() {
       let that=this;
	console.log(1);
      try {
        const res = await this.fetchData()
        if (this.isRefreshing) {
          this.listData = res.data
	  setTimeout(() => {
	 
	   that.isRefreshing = false
	   }, 500);
        } else {
			this.isLoadingMore=false;
          this.listData = [...this.listData, ...res.data]
		  setTimeout(() => {
		   that.isLoadingMore = false
		  
		   }, 500);
        }
        this.hasMore = res.hasMore
        this.page++
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }  
    },
    
   async fetchData() {
		let res = await request.post('zsd/list', {"lessonid":this.lessonid,page:this.page,pageSize:this.pageSize});
	   let hasmore=true;
	   if(res.data.length==0){
		   hasmore=false;
	   }
		return {
           data: res.data,
            hasMore:hasmore // 模拟只有3页数据
          }
      
    },
    
    loadMore() {
      if (!this.hasMore || this.isLoadingMore) return
      this.isLoadingMore = true
      this.loadData()
    },
    
    onRefresh() {
      this.isRefreshing = true
      this.page = 1
      this.loadData();
    }
  }
}
</script>

<style lang="scss">
.list-container {
  height: 100vh;
  background: #f5f5f5;
}

.scroll-view {
  height: 100%;
}

.list-item {
  background: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 30rpx;
}

.item-content {
  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    text {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
    
    .status-tag {
      font-size: 24rpx;
      color: #fff;
      background: #ff6b6b;
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
    }
  }
  
  .item-info {
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    
    text {
      font-size: 26rpx;
      color: #999;
    }
  }
}

.loading-more, .no-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}
</style>