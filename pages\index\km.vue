<template>
  <view class="subject-detail">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="title">{{lesson.name}}</text>
        <text class="score">{{score}}分</text>
      </view>
    </view>

    <!-- 历史成绩走势 -->
    <view class="history-card">
      <view class="card-title">历史成绩走势</view>
      <qiun-data-charts 
        type="line"
        :opts="chartOpts"
        :chartData="chartData"
        :ontouch="true"
        :canvas2d="true"
        canvasId="lineChart"
      />
    </view>

    <!-- 科目成绩树形结构 -->
    <view class="score-tree" style="margin-top: 80rpx;">
      <view class="card-title">成绩详情</view>
      <view v-for="(item, index) in scoreTree" :key="index" class="tree-item">
        <view class="parent-node" @click="item.expanded = !item.expanded">
          <text class="node-name">{{item.name}}</text>
          <text class="node-score">{{item.score}}分</text>
          <text class="expand-icon">{{item.expanded ? '▼' : '▶'}}</text>
        </view>
        <view v-if="item.expanded" class="child-nodes">
          <view v-for="(child, childIndex) in item.children" 
                :key="childIndex" 
                class="child-item">
            <text class="node-name">{{child.name}}</text>
            <text class="node-score">{{child.score}}分</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BASE_URL from '@/api/env.js'
import request from '@/utils/request.js'
export default {
  name: 'SubjectDetail',
  data() {
    return {
      lessonId:0,
      lesson:{},
      score:0,
      subject: {
        name: '高等数学',
        score: 89
      },
      chartData: {
        categories: [ ],
        series: [{
          name: "成绩",
          data: [ ],
          format: "value"
        }]
      },
      chartOpts: {
        rotate: false,
        rotateLock: true,
        dataLabel: true,
        dataPointShape: true,
        enableScroll: true,
        enableMarkLine: false,
        animation: true,
        timing: 'easeOut',
        duration: 1000,
        padding: [15, 15, 0, 15],
        legend: {
          show: false
        },
        xAxis: {
          disableGrid: true,
          scrollShow: true,
          itemCount: 5,
          boundaryGap: 'center',
          axisLine: true,
          axisLineColor: '#cccccc',
          labelCount: 4
        },
        yAxis: {
          data: [{
            min: 0,
            max: 100,
            title: '分数',
            titleFontSize: 12,
            format: 'value',
            gridType: 'dash',
            dashLength: 4,
            splitNumber: 5
          }]
        },
        extra: {
          line: {
            type: 'curve',
            width: 2,
            activeType: 'hollow',
            linearType: 'custom',
            color: ['#1890ff'],
            activeColor: '#1890ff'
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'dash',
            dashLength: 4,
            gridColor: '#1890ff',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#000000',
            labelBgOpacity: 0.7,
            labelFontColor: '#FFFFFF'
          }
        },
        height: 400
      },
      scoreTree: [ 
      ]
    }
  },
  onLoad(options) {
    this.lessonId=options.id;
    this.score=options.score;
    this.getLesson();
  },
  methods: {
    async getLesson() {
      let res = await request.post('index/lessoninfo', {id:this.lessonId});
      console.log(res);
      this.lesson=res;
      let score = res.history.map(item => item.score) 
      let date = res.history.map(item => item.create_date) 
      
      this.chartData.series[0].data = score;
      this.chartData.categories = date;
      this.scoreTree=res.tree.children
 
    },
    updateChartData(newData) {
      this.chartData = {
        categories: newData.map(item => item.date),
        series: [{
          name: "成绩",
          data: newData.map(item => item.score),
          format: "value"
        }]
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.subject-detail {
  padding: 20rpx;
  
  .info-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .score {
        font-size: 36rpx;
        color: #1890ff;
        font-weight: bold;
      }
    }
  }
  
  .history-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 40rpx;
    margin-bottom: 20rpx;
    height: 500rpx;
    
    .card-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
  }
  
  .score-tree {
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-top: 20rpx;
    
    .card-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .tree-item {
      .parent-node {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        background: #f5f5f5;
        margin-bottom: 10rpx;
        border-radius: 8rpx;
        
        .node-name {
          flex: 1;
        }
        
        .node-score {
          margin: 0 20rpx;
          color: #1890ff;
        }
        
        .expand-icon {
          width: 40rpx;
          text-align: center;
        }
      }
      
      .child-nodes {
        padding-left: 40rpx;
        
        .child-item {
          display: flex;
          justify-content: space-between;
          padding: 16rpx;
          border-bottom: 1rpx solid #eee;
          
          &:last-child {
            border-bottom: none;
          }
          
          .node-name {
            flex: 1;
            color: #666;
          }
          
          .node-score {
            color: #1890ff;
          }
        }
      }
    }
  }
}
</style>