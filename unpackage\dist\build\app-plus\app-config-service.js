
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login","pages/answer/intro","pages/reg","pages/updateinfo","pages/index/index","pages/shop/detail","pages/other/yhxy","pages/other/ysxy","pages/other/aboutUs","pages/other/order","pages/index/page2","pages/essay/edit","pages/essay/score","pages/index/main","pages/index/page3","pages/index/page4","pages/answer/list","pages/answer/start","pages/other/wtfk","pages/answer/zsd","pages/answer/zsdinfo","pages/answer/learn","pages/answer/learninfo","pages/answer/learnlist","pages/answer/see","pages/other/wtfk2","pages/index/km","pages/answer/seeinfo","pages/answer/adintro","pages/webview/index/index"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-app","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8"},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"weex","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"极思教育","compilerVersion":"4.75","entryPagePath":"pages/login","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login","meta":{"isQuit":true},"window":{"navigationBarTitleText":"登录","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/answer/intro","meta":{},"window":{"navigationBarTitleText":"教师介绍"}},{"path":"/pages/reg","meta":{},"window":{"navigationBarTitleText":"注册","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/updateinfo","meta":{},"window":{"navigationBarTitleText":"更新信息","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/index","meta":{},"window":{"navigationBarTitleText":"首页","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/shop/detail","meta":{},"window":{"navigationBarTitleText":"商品详情页","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/other/yhxy","meta":{},"window":{"navigationBarTitleText":"用户协议","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/other/ysxy","meta":{},"window":{"navigationBarTitleText":"隐私协议","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/other/aboutUs","meta":{},"window":{"navigationBarTitleText":"关于我们","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/other/order","meta":{},"window":{"navigationBarTitleText":"订单列表","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/page2","meta":{},"window":{"navigationBarTitleText":"","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/essay/edit","meta":{},"window":{"navigationBarTitleText":"上传文章","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/essay/score","meta":{},"window":{"navigationBarTitleText":"文章评分","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/main","meta":{},"window":{"navigationBarTitleText":"主页","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/page3","meta":{},"window":{"navigationBarTitleText":"页面","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/page4","meta":{},"window":{"navigationBarTitleText":"页面","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/answer/list","meta":{},"window":{"navigationBarTitleText":"答题列表","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/answer/start","meta":{},"window":{"navigationBarTitleText":"开始答题","navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/other/wtfk","meta":{},"window":{"navigationBarTitleText":"题目纠错"}},{"path":"/pages/answer/zsd","meta":{},"window":{"navigationBarTitleText":"知识点"}},{"path":"/pages/answer/zsdinfo","meta":{},"window":{"navigationBarTitleText":"知识点"}},{"path":"/pages/answer/learn","meta":{},"window":{"navigationBarTitleText":"页面","navigationBarBackgroundColor":"#ffffff","navigationStyle":"custom"}},{"path":"/pages/answer/learninfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/answer/learnlist","meta":{},"window":{"navigationBarTitleText":"题目列表"}},{"path":"/pages/answer/see","meta":{},"window":{"navigationBarTitleText":"页面","navigationBarBackgroundColor":"#ffffff","navigationStyle":"custom"}},{"path":"/pages/other/wtfk2","meta":{},"window":{"navigationBarTitleText":"问题反馈"}},{"path":"/pages/index/km","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/answer/seeinfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/answer/adintro","meta":{},"window":{"navigationBarTitleText":""}},{"path":"/pages/webview/index/index","meta":{},"window":{"navigationBarTitleText":""}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
