{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__B4B7003", "name": "极思教育", "version": {"name": "1.0.27", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Camera": {}, "VideoPlayer": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": true, "delay": 0, "target": "id:1", "waiting": true}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"minSdkVersion": 21, "permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "packagename": "uni.app.UNIB4B7003", "aliasname": "", "password": "", "keystore": "html5plus://test", "custompermissions": true}, "apple": {"dSYMs": false, "devices": "universal"}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}, "orientation": "portrait-primary"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "launch_path": "__uniappview.html", "adid": "121119270012"}, "screenOrientation": ["portrait-primary"]}