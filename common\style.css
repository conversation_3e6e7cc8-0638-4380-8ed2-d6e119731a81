/**app.wxss**/
page,view,scroll-view,swiper,swiper-item,movable-area,movable-view,cover-view,cover-image,icon,text,rich-text,progress,button,checkbox-group,checkbox,form,input,label,picker,picker-view,radio-group,radio,slider,switch,textarea,navigator,functional-page-navigator,image,video,camera,live-player,live-pusher,map,canvas,open-data,web-view,ad{box-sizing: border-box;}
/* ------  normal主色调 border边框颜色  ------ */
page{word-break: break-all;
  --normal: #10C194;
  --border: #EEEEEE;
  --arrow: #ccc;
  --nothing:#0d8aff;
  --red: #FE3846;
  --blue: #004794;
  --green: #1FB7B2;
  --yellow: #FDCC25;
  --black: #101010;
  --darkGray: #333333;
  --gray: #898989;
  --ghostWhite: #F0F2F5;
  --bggrey:#F7F9FC;
  --white: #ffffff;}
.container{width: 100%;font-family:'PingFang SC','Microsoft Yahei';line-height: 1.4;font-size: 28rpx;color: var(--black);background-color: Var(--white);overflow: hidden;position: relative;min-height: 100vh;box-sizing: border-box;}
.page-head{ position: fixed; left: 0; right: 0; top: 0; z-index: 999;overflow: hidden;padding-top: var(--status-bar-height);height: calc(88rpx + var(--status-bar-height));}
.page-foot{ position: fixed; left: 0; right: 0; bottom: 0; z-index: 999;}
/*IPhoneX适配*/
@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .container{
        padding-bottom: calc(68rpx/2);
        padding-bottom: calc(constant(safe-area-inset-bottom)/2);
        padding-bottom: calc(env(safe-area-inset-bottom)/2);
    }
  .page-foot{
        padding-bottom: 68rpx;
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }
    .page-foot ~ .container{
        padding-bottom: calc(100rpx + 68rpx);
        padding-bottom: calc(constant(safe-area-inset-bottom) + 100rpx);
        padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
    }
}
/* 相对定位 */
.pr{position: relative}
/* 左右浮动 */
.fl{float:left;}
.fr{float:right;}
/* 组件 - 模拟表格布局 */
.table{width:100%;display:table;table-layout:fixed;}
.table-cell{display:table-cell;vertical-align:middle;}
.table-cell.vt{vertical-align:top;}
.table-cell.vb{vertical-align:bottom;}
/* 按钮 */
button{padding: 0;margin: 0;text-align: center;background-color: transparent;border: none;width: auto;}
/*flex布局*/
.flex{display: flex;}
.flex-box{display: flex;align-items: center;}
.flex-grow-1{flex: 1;overflow: hidden;}
.flex-wrap{ flex-wrap: wrap;}
.flex-between{ justify-content: space-between;}
.flex-around{ justify-content: space-around;}
.flex-center{ justify-content: center;}
.flex-col{flex-direction: column;}
.flex-col-reserve{flex-direction: column-reverse;}
.flex-row{flex-direction: row;}
.flex-row-reserve{flex-direction: row-reverse;}
.flex-align-start{align-items: flex-start;}
.flex-end{justify-content: flex-end;}
.flex-col-end{align-items: flex-end;}
/*清除浮动*/
.clearfix{display:block;}
.clearfix:after{content:" ";display:block;height:0;clear:both;visibility:hidden;overflow:hidden;}
/*1像素边框*/
/* 用法 外层class="m-hairline" 需要设置边框的元素class="m-hairline--left" */
.m-hairline,.m-hairline--bottom,.m-hairline--left,.m-hairline--right,.m-hairline--surround,.m-hairline--top,.m-hairline--top-bottom{position:relative;}
.m-hairline--bottom:after,.m-hairline--left:after,.m-hairline--right:after,.m-hairline--surround:after,.m-hairline--top-bottom:after,.m-hairline--top:after,.m-hairline:after{content:" ";position:absolute;top:0;left:0;width:200%;height:200%;border:0 var(--border) solid;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;pointer-events:none;box-sizing:border-box;}
.m-hairline--top:after{border-top-width:1px;}
.m-hairline--left:after{border-left-width:1px;}
.m-hairline--right:after{border-right-width:1px;}
.m-hairline--bottom:after{border-bottom-width:1px;}
.m-hairline--top-bottom:after{border-width:1px 0;}
.m-hairline--surround:after{border-width:1px;}
/*阴影*/
.m-shadow{box-shadow:0 1rpx 18rpx 0 rgba(0,0,0,0.06);}
/*分割线*/
.m-baseline{ display: flex; align-items: center; justify-content: center; padding: 30rpx; font-size: 20rpx; color: #e1e1e1; }
.m-baseline:before,
.m-baseline:after{ content: ""; display: block; flex: 1; height: 1px; background-color: currentColor; transform: scaleY(0.5); }
.m-baseline:before{ margin-right: 20rpx; }
.m-baseline:after{ margin-left: 20rpx; }
/*箭头*/
.m-arrow-right,
.m-arrow-down,
.m-arrow-up {position: relative;padding-right: 20rpx;}
.m-arrow-right:after,
.m-arrow-down:after,
.m-arrow-up:after {content: "";display: inline-block;height: 14rpx;width: 14rpx;border-width: 3rpx 3rpx 0 0;border-color: var(--arrow);border-style: solid;position: absolute;top: 50%;right: 4rpx;box-sizing: border-box;}
.m-arrow-right:after {transform: matrix(.71, .71, -.71, .71, 0, 0);margin-top: -6rpx;}
.m-arrow-down:after {transform: matrix(-.71, 0.71, -.71, -.71, 0, 0);margin-top: -15rpx;}
.m-arrow-up:after {transform: matrix(0.71, -.71, 0.71, 0.71, 0, 0);margin-top: -3rpx;}
/*文本省略*/
.m-ellipsis{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.m-ellipsis-l2,.m-ellipsis-l3{display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;}
.m-ellipsis-l2{-webkit-line-clamp:2;}
.m-ellipsis-l3{-webkit-line-clamp:3;}
/* 组件 - 表单 */
.form-unify{display:block;width:100%;height:auto;padding:20rpx 24rpx;border:0;background-color:transparent;background-image:none;font-family:'Microsoft YaHei';line-height:1.4;-webkit-border-radius:4px;border-radius:4px;box-sizing:border-box;}
/* 组件 - 提示 */
.hint-num{ position: relative;}
.hint-num text{ display: block; min-width:17rpx; padding: 6rpx 10rpx;  background: var(--red); font-style: normal; text-align: center; font-size: 20rpx;line-height: 20rpx; color: var(--white); border-radius: 100rpx; -webkit-border-radius: 100rpx; position: absolute; top: 0; right: 40rpx; additive-symbols:border-box; }
.hint-txt{position: relative;}
.hint-txt:after{ content: ""; display: inline-block; width: 12rpx; height: 12rpx; background-color: var(--red);border-radius: 50%;position: absolute;right: 0;top: 0; }
/* 红色星号*必填项 */
.m-must::after{ content: "*"; display: inline-block; vertical-align: middle; margin-left: 10rpx; color: var(--red);}
/* 无数据 */
.nothing{padding: 60rpx 0;text-align: center;}
.nothing image{display: block;width: 160rpx;height: 160rpx;margin: 0 auto;}
.nothing text{display: block;margin-top: 20rpx;text-align: center;line-height: 60rpx;font-size: 26rpx;color: var(--nothing);}
.nothing.hide{display: none;}
/* 通用样式 */
.fwb{font-weight: bold}
.tc{text-align: center}
.tr{text-align: right}
.tl{text-align: left}
/* 圆角 */
.br10{border-radius: 10rpx}
.br20{border-radius: 20rpx}
.br30{border-radius: 30rpx}
.br40{border-radius: 40rpx}
.br50{border-radius: 50rpx}
.brarc{border-radius: 50%}
/* 字体颜色 */
.colf{color: #fff}
.colc{color: #ccc}
.col0{color: #000}
.col3{color: #333}
.col5{color: #555}
.col6{color: #666}
.col9{color: #999}
.cold{color: #ddd}
.cola{color:#aaa}
/* 字体大小 */
.fs10{font-size: 10rpx}
.fs12{font-size: 12rpx}
.fs14{font-size: 14rpx}
.fs16{font-size:16rpx}
.fs18{font-size:18rpx}
.fs20{font-size: 20rpx}
.fs22{font-size:22rpx}
.fs24{font-size: 24rpx}
.fs26{font-size: 26rpx}
.fs28{font-size: 28rpx}
.fs30{font-size: 30rpx}
.fs32{font-size: 32rpx}
.fs34{font-size: 34rpx}
.fs36{font-size: 36rpx}
.fs38{font-size: 38rpx}
.fs40{font-size: 40rpx}
.fs42{font-size: 42rpx}
.fs44{font-size: 44rpx}
.fs46{font-size: 46rpx}
.fs48{font-size: 48rpx}
.fs50{font-size: 50rpx;}
/* padding */
.p5{padding: 5rpx;}
.p10{padding: 10rpx;}
.p15{padding: 15rpx;}
.p20{padding: 20rpx;}
.p25{padding: 25rpx;}
.p30{padding: 30rpx;}
.p35{padding: 35rpx;}
.p40{padding: 40rpx;}
.p50{padding: 50rpx;}
.pb5{padding-bottom: 5rpx}
.pb10{padding-bottom: 10rpx}
.pb15{padding-bottom: 15rpx}
.pb20{padding-bottom: 20rpx}
.pb25{padding-bottom: 25rpx}
.pb30{padding-bottom: 30rpx}
.pb35{padding-bottom: 35rpx}
.pb40{padding-bottom: 40rpx}
.pb50{padding-bottom: 50rpx}
.pb70{padding-bottom: 70rpx;}
.pb80{padding-bottom: 80rpx;}
.pb100{padding-bottom: 100rpx}
.pt5{padding-top: 5rpx}
.pt10{padding-top: 10rpx}
.pt15{padding-top: 15rpx}
.pt20{padding-top: 20rpx}
.pt25{padding-top: 25rpx}
.pt30{padding-top: 30rpx}
.pt35{padding-top: 35rpx}
.pt40{padding-top: 40rpx}
.pt50{padding-top: 50rpx}
.pt70{padding-top: 70rpx}
.pl5{padding-left: 5rpx}
.pl10{padding-left: 10rpx}
.pl15{padding-left: 15rpx}
.pl20{padding-left: 20rpx}
.pl25{padding-left: 25rpx}
.pl30{padding-left: 30rpx}
.pl35{padding-left: 35rpx}
.pl40{padding-left: 40rpx}
.pr5{padding-right: 5rpx}
.pr10{padding-right: 10rpx}
.pr15{padding-right: 15rpx}
.pr20{padding-right: 20rpx}
.pr25{padding-right: 25rpx}
.pr30{padding-right: 30rpx}
.pr35{padding-right: 35rpx}
.pr40{padding-right: 40rpx}
.plr5{padding-left: 5rpx;padding-right: 5rpx}
.plr10{padding-left: 10rpx;padding-right:10rpx}
.plr15{padding-left: 15rpx;padding-right:15rpx}
.plr20{padding-left: 20rpx;padding-right:20rpx}
.plr25{padding-left: 25rpx;padding-right:25rpx}
.plr30{padding-left: 30rpx;padding-right:30rpx}
.plr35{padding-left: 35rpx;padding-right:35rpx}
.plr40{padding-left: 40rpx;padding-right:40rpx}
.plr50{padding-left: 50rpx;padding-right:50rpx}
.ptb5{padding-top: 5rpx;padding-bottom: 5rpx}
.ptb10{padding-top: 10rpx;padding-bottom:10rpx}
.ptb15{padding-top: 15rpx;padding-bottom:15rpx}
.ptb20{padding-top: 20rpx;padding-bottom:20rpx}
.ptb25{padding-top: 25rpx;padding-bottom:25rpx}
.ptb30{padding-top: 30rpx;padding-bottom:30rpx}
.ptb35{padding-top: 35rpx;padding-bottom:35rpx}
.ptb40{padding-top: 40rpx;padding-bottom:40rpx}
/* margin */
.m5{margin: 5rpx;}
.m10{margin: 10rpx;}
.m15{margin: 15rpx;}
.m20{margin: 20rpx;}
.m25{margin: 25rpx;}
.m30{margin: 30rpx;}
.m35{margin: 35rpx;}
.m40{margin: 40rpx;}
.mb5{margin-bottom: 5rpx}
.mb10{margin-bottom: 10rpx}
.mb15{margin-bottom: 15rpx}
.mb20{margin-bottom: 20rpx}
.mb25{margin-bottom: 25rpx}
.mb30{margin-bottom: 30rpx}
.mb35{margin-bottom: 35rpx}
.mb40{margin-bottom: 40rpx}
.mb50{margin-bottom: 50rpx}
.mb55{margin-bottom: 55rpx}
.mb70{margin-bottom: 70rpx}
.mt5{margin-top: 5rpx}
.mt10{margin-top: 10rpx}
.mt15{margin-top: 15rpx}
.mt20{margin-top: 20rpx}
.mt25{margin-top: 25rpx}
.mt30{margin-top: 30rpx}
.mt35{margin-top: 35rpx}
.mt40{margin-top: 40rpx}
.ml5{margin-left: 5rpx}
.ml10{margin-left: 10rpx}
.ml15{margin-left: 15rpx}
.ml20{margin-left: 20rpx}
.ml25{margin-left: 25rpx}
.ml30{margin-left: 30rpx}
.ml35{margin-left: 35rpx}
.ml40{margin-left: 40rpx}
.mr5{margin-right: 5rpx}
.mr10{margin-right: 10rpx}
.mr15{margin-right: 15rpx}
.mr20{margin-right: 20rpx}
.mr25{margin-right: 25rpx}
.mr30{margin-right: 30rpx}
.mr35{margin-right: 35rpx}
.mr40{margin-right: 40rpx}
.mlr5{margin-left: 5rpx;margin-right: 5rpx}
.mlr10{margin-left: 10rpx;margin-right:10rpx}
.mlr15{margin-left: 15rpx;margin-right:15rpx}
.mlr20{margin-left: 20rpx;margin-right:20rpx}
.mlr25{margin-left: 25rpx;margin-right:25rpx}
.mlr30{margin-left: 30rpx;margin-right:30rpx}
.mlr35{margin-left: 35rpx;margin-right:35rpx}
.mlr40{margin-left: 40rpx;margin-right:40rpx}
.mtb5{margin-top: 5rpx;margin-bottom: 5rpx}
.mtb10{margin-top: 10rpx;margin-bottom:10rpx}
.mtb15{margin-top: 15rpx;margin-bottom:15rpx}
.mtb20{margin-top: 20rpx;margin-bottom:20rpx}
.mtb25{margin-top: 25rpx;margin-bottom:25rpx}
.mtb30{margin-top: 30rpx;margin-bottom:30rpx}
.mtb35{margin-top: 35rpx;margin-bottom:35rpx}
.mtb40{margin-top: 40rpx;margin-bottom:40rpx}



/* 字体颜色 */
.red {color: var(--red);}
.yellow {color: var(--yellow);}
.blue {color: var(--blue);}
.green {color: var(--green);}
.black {color: var(--black);}
.col-normal {color: var(--normal);}
.col-darkGray {color: var(--darkGray);}
.col-gray {color: var(--gray);}

/* 背景颜色 (根据项目自行添加) */
.bg-white{background-color: var(--white);}
.bg-ghostWhite{background-color: var(--ghostWhite);}

/* 文章详情正文统一32rpx */
/* 页面布局
    <view class="page-head"></view>
    <view class="page-foot"></view>
    <view class="container"></view> 
*/

/* 所有颜色根据写在body里的 （--名称：颜色）如 --border:#f9f9f9;为基准 */

/* 自己额外定义的公共样式 */
/* .bg-1{background-color: var(--bg1);}
.m-button.btn1{background-color: var(--btn1);color: var(--white);} */

/* 自定义颜色 */
.col3{color: #333;}
.col9e{color: #9e9e9e;}
.col89{color: #898989;}
.cold6{color: #d6d6d6;}
.col03{color: #030303;}
.cola7{color: #a7a7a7;}
.cold8{color: #d8d8d8;}
.col80{color: #808080;}
.lightblue{color: #007AFF;}
.orange{color: #FE973C;}
/* btn 字体大小颜色 按钮背景颜色 margin */
.xilu button{padding: 0;margin: 0;border: none;}
.xilu button::after{border: none;}
.btn1{background: #FFC100;border-radius: 50rpx;font-weight: 400;color: #333;text-align: center;border: 1rpx solid #FFC100;}
.btn2{background: #FFF3CC;border-radius: 50rpx;font-weight: 400;color: #333;text-align: center;border: 1rpx solid  #FFF3CC;}
.btn4{background: #ffffff;border-radius: 50rpx;font-weight: 400;color: #333;text-align: center;border: 1rpx solid #ccc;}
.btn3{background: #f7f9fc;border-radius: 50rpx;font-weight: 400;color: #333;text-align: center;height: 56rpx;line-height: 56rpx;color: #aaa;font-size: 24rpx;width: 110rpx;}
.page-foot .btn1 {width: 650rpx;height: 80rpx;border-radius: 40rpx;background: #ffc100;background-blend-mode: normal;margin: 10rpx auto;font-size: 30rpx;line-height: 78rpx;}
.phone{width: 30rpx;height: 30rpx;}
.relative{position: relative;}
.bg{width: 750rpx;position: relative;z-index: 999;}
button{border: none;line-height: inherit;padding: 0;margin: 0;font-size: 28rpx;box-shadow: none;}
.bottom_pop{padding: 50rpx 0;}
.ptb50{padding-top: 50rpx;padding-bottom: 50rpx;}
.w100{width: 100vw;}