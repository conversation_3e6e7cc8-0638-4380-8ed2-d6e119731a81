<template>
	<view class="mainBox" style="padding-bottom: 200rpx;">
		<view class="bgBox">
			<view class="zthH"></view>
			
			<view style="display: flex; justify-content: space-between; align-items: center;">

				<view class="text-black text-bold text-lg text-shadow" style="color: #222; font-family: z1 !important;">🌳
					极思慧学·助力高效学习</view>
				<view style="color: #222; font-family: z1 !important;"> </view>
			</view>
				<view class="ad-container" v-if="adList.length > 0">
				<swiper class="ad-swiper" 
						:indicator-dots="true" 
						:autoplay="true" 
						:interval="3000" 
						:duration="500" 
						:circular="true">
					<swiper-item v-for="(item, index) in adList" :key="index">
					<view class="ad-item" @click="handleAdClick(item)">
						<image :src="item.imageUrl" mode="aspectFill" class="ad-image"></image>
						<view class="ad-title" v-if="item.name">{{item.name}}</view>
					</view>
					</swiper-item>
				</swiper>
				</view>
			
			
		<view class="flex justify-between" style="margin-top: 14rpx;">
					<view class="topNav2 text-left flex justify-start" @click="sk">
						<image src="../../static/icon_dt3.svg" mode="heightFix" style="height: 60rpx;margin-top: 6rpx;"></image>
						<view class="flex justify-around flex-direction" style="margin-left: 12rpx;">
							<view class="text-bold text-df" style="font-family: z1 !important;">上课</view>
							<view class="text-xs" style="color: #666;">查看上课内容</view>
						</view>
					</view>
					<view class="topNav2 text-left flex justify-start"  @click="pgt">
						<image src="../../static/icon_ks4.svg" mode="heightFix" style="height: 60rpx;margin-top: 6rpx;"></image>
						<view class="flex justify-around flex-direction" style="margin-left: 12rpx;">
							<view class="text-bold text-df" style="font-family: z1 !important;">
								批改题
								<text v-if="count.num3 > 0" class="badge">{{count.num3}}</text>
							</view>
							<view class="text-xs" style="color: #666;">查看批改题</view>
						</view>
					</view>
				</view>
				
				
			<view class="flex flex-direction margin-top" >
			 

				<!-- 第一行：练习量图表 -->
				<view class="topNav" style="margin-bottom: 14rpx;height: 400rpx; width: 100%;">
				<!-- 添加统计信息展示 -->
	<view class="stats-header">
  <view class="stat-item">
    <text class="stat-label">总练习天数</text>
    <text class="stat-number green">{{count.num2}}</text>
  </view>
  <view class="stat-item">
    <text class="stat-label">总练习题目</text>
    <text class="stat-number blue">{{count.num1}}</text>
  </view>
</view>
					<view class="text-df flex justify-between"  >
						<view style="font-family: z1 !important;vertical-align:middle;margin-top: 4rpx;">
							<text class="text-bold">练习量</text>
							<text class="text-bold" style="margin-left: 4rpx;">(近7天)</text>
						</view>
						<image style="width: 40rpx;vertical-align:middle;margin-top: -6rpx ;"
							src="/static/uchartsIcon5.png" mode="widthFix"></image>
					</view>
					<view class="text-center" style="height: 500rpx;">
						<canvas @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" 
							canvas-id="canvasColumn" id="canvasColumn" class="charts"
							style="width: 100%;height: 500rpx;"></canvas>
					</view>
				</view>

				<!-- 第二行：上课和批改题按钮 -->
		
			</view>

		 <view class="bottomBox"  >
		 	<view class="title-header">
		 		<view class="title-text">
		 			 科 · 目 · 分 · 数
		 		</view>
		 	</view>
		 
		 </view>
		 
		 
		 
		 
		 <scroll-view class="scroll-view" scroll-x="true">
		 	<view  class="item bg-white shadow-warp" v-for="(item,index) in lessonScore" :key="index">
		 		<view class="ucharsItem padding-sm" @click="gokm(item.lessonid,item.score)">
		 			<view class="flex justify-between">
		 				<view>
		 					<text class="lg text-green cuIcon-titles"></text>
		 					<text class="text-bold text-smdf">{{item.lessonname}}</text>
		 				</view>
		 				<view class="text-grey" style="font-size: 22rpx;">
		 					{{item.class}}
		 				</view>
		 			</view>
		 			<view class="progress-circle" :style="{ '--progress': getProgress(item.score) }">
		 				<view class="progress-circle-inner">
		 					<text class="score">{{item.score}}</text>
		 					<text class="unit">平均分</text>
		 				</view>

					

					 
		 			</view>
						<view class="expire-info">
							<view class="expire-container">
								<view class="expire-icon">
									<text class="cuIcon-time"></text>
								</view>
								<view class="expire-content">
									<text class="expire-label">{{item.enddate === '已过期' ? '会员状态' : '会员有效期至'}}</text>
									<text class="expire-date" :class="{ 'expired': item.enddate === '已过期' }">{{item.enddate}}</text>
								</view>
							</view>
						</view>
		 		</view>
		 	</view>
		 </scroll-view>
		 
		</view>




	
<u-popup v-model="qrCodeVisible" mode="center">
			<view class="qr-popup padding-lg">
				<view class="text-center padding-bottom">
					<text class="text-lg text-bold">到期提醒</text>
				</view>
				 
				<view class="text-center padding-top-sm">
					<text class="text-df">有科目会员过期，该科目的部分功能将受到限制，且成绩为暂存状态，成绩参考不准确，为了不影响您的使用，请到公众号充值会员开通</text>
				</view>
			 
			</view>
		</u-popup>



		<Footer style="z-index: 99999;" :footState="0" :num="messageCount"></Footer>
	</view>
</template>

<script>
import BASE_URL from '@/api/env.js'
	// 图表
	import uCharts from "@/components/u-charts/u-charts.js";
	var uChartsInstance = {};
	import request from '@/utils/request';
	import moment from "@/components/moment/index.js"; // 格式化时间 插件
	import Footer from "@/components/footer/footer.vue";
import { getStorageSync } from "../../utils/utils";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				  BASE_URL,
				qrCodeVisible:false,
				user:null,
				messageCount: 0,
				tree1: 1,
				cWidth: '',
				cHeight: '',
				lessonScore: [],
				notvip:false,
				count:{
					num1:0,
					num2:0,
					num3:0
				},
				totalDays: 2,
				totalQuestions: 152,
		      adList: [],
				touchMoveTimer: null // 添加触摸移动定时器，用于性能优化
			}
		},
		onLoad() {
			
			
			this.user=getStorageSync('user');
			
			 
			this.cWidth = uni.upx2px(750); // 优化画布宽度，平衡显示效果和性能
			this.cHeight = uni.upx2px(300);
			this.getServerData();
			//this.getMainData();
		},
		
		
async	onShow() {
				let res = await request.post('bk/count', {});
				this.messageCount=res.data.count;
				
				let res2=await request.post('index/indexnum', {});
			 
				this.count.num1=res2.count1;
				this.count.num2=res2.count2;
				this.count.num3 = res2.count3;


				let res3=await request.post('index/lessonscore', {});
				console.log(res3);
				this.lessonScore = res3;

				//如果有课程到期进行提示
				if (!uni.getStorageSync('hasShownExpiredTip')) {
					const hasExpired = this.lessonScore.some(item => item.enddate === '已过期');
					if (hasExpired) {
						this.qrCodeVisible = true;
						uni.setStorageSync('hasShownExpiredTip', true);
					}
				}

				//如果所有课程都到期，notvip=true
				const allExpired = this.lessonScore.every(item => item.enddate === '已过期');	
				if (allExpired) {
					this.notvip = true;
				}



		},
		onUnload() {
			// 清理定时器，避免内存泄漏
			if (this.touchMoveTimer) {
				clearTimeout(this.touchMoveTimer);
				this.touchMoveTimer = null;
			}
			// 清理图表实例
			for (let key in uChartsInstance) {
				if (uChartsInstance[key]) {
					uChartsInstance[key] = null;
				}
			}
		},
		onShareAppMessage() {

		},
		onShareTimeline() {

		},
		methods: {

			async handleAdClick(item) {

				if(item.clickable==1){
					console.log(item);
					 
					 if(item.link_url){//webview打开网址
						uni.navigateTo({
							url: '/pages/webview/index/index?url='+item.link_url
						})
					}

				}
			},
			golearn(){
				
				uni.navigateTo({
					url: '/pages/answer/learn' 
				})
				
				
				
				
			},
		 
			drawChartsMain(id, data) {
				const ctx = uni.createCanvasContext(id, this);
				let cWidth_main = uni.upx2px(700);
				let cHeight_main = uni.upx2px(300);
				uChartsInstance[id] = new uCharts({
					type: "radar",
					context: ctx,
					width: cWidth_main,
					height: cHeight_main,
					categories: data.categories,
					series: data.series,
					canvas2d: true,
					animation: true,
					background: "#FFFFFF",
					color: ["#2fc25b", "#1890FF", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [5, 5,5, 5],
					dataLabel: false,
					enableScroll: false,
					legend: {
						show: true,
						position: "right",
						lineHeight: 25
					},
					extra: {
						radar: {
							gridType: "radar",
							gridColor: "#CCCCCC",
							gridCount: 3,
							opacity: 0.2,
							max: 100,
							border: true
						}
					}
				});
			},

		async	getServerData() {

			let adList=await request.post('ad/list', {});
			this.adList=adList.data;
			

			for(let i=0;i<this.adList.length;i++){
				this.adList[i].imageUrl=this.BASE_URL+this.adList[i].image;
			}



				let res=await request.post('user/tongji', {});
				console.log(res);
			 
				this.drawCharts('canvasColumn', res);
			},
			drawCharts(id, data) {
				// 处理数据，确保所有数据都能显示
				const processedData = this.processChartData(data);

				const ctx = uni.createCanvasContext(id, this);
				uChartsInstance[id] = new uCharts({
					type: "column",
					ontouch: true,
					context: ctx,
					width: this.cWidth,
					height: this.cHeight,
					dataPointShape: false,
					canvas2d: false, // 改为false，提高性能
					enableScroll: true,
					touchMoveLimit: 120, // 降低触摸频率，减少卡顿
					fontSize: 11,
					categories: processedData.categories,
					series: processedData.series,
					animation: false, // 禁用动画
					background: "#FFFFFF",
					color: ["#2fc25b"],
					padding: [15, 20, 10, 10],
					fontColor: "#666",
					legend: {
						show: false,
					},
					xAxis: {
						disableGrid: true,
						itemCount: 8, 
						fontSize: 10,
						scrollAlign: 'left',
						axisLineColor: '#999',
						scrollShow: false, // 禁用滚动条显示，减少渲染负担
						boundaryGap: true,
						labelShow: true, // 确保标签显示
						fontColor: '#666' // 设置标签颜色
					},
					yAxis: {
						disabled: true,
						disableGrid: true,
					},
					extra: {
						column: {
							type: "group",
							width: 8,
							seriesGap: 2,
							activeBgOpacity: 0, // 禁用激活背景，减少重绘
							linearOpacity: 1, // 简化透明度
							labelShow: false, // 禁用标签显示，减少渲染
							linearType: "none", // 简化线性类型
							activeBgColor: "transparent", // 透明激活背景
							barBorderCircle: false, // 禁用圆角，减少渲染复杂度
							customColor: ["#1cbbb4"],
							categoryGap: 1
						}
					}
				});
			},

			// 处理图表数据，确保所有数据都能显示
			processChartData(data) {
				if (!data || !data.categories || !data.series) {
					return data;
				}

				// 复制原始数据
				const processedData = {
					categories: [...data.categories],
					series: data.series.map(serie => ({
						...serie,
						data: [...serie.data]
					}))
				};

				const itemCount = 5; // 与xAxis.itemCount保持一致
				const dataLength = processedData.categories.length;

				console.log(`原始数据长度: ${dataLength}, itemCount: ${itemCount}`);

				// 如果数据长度大于itemCount，说明需要滚动查看，添加空白数据确保最后的数据能完整显示
				if (dataLength > itemCount) {
					// 添加1-2个空白数据点，确保最后的真实数据能完整显示
					const paddingCount = Math.min(2, Math.ceil(dataLength * 0.2)); // 添加20%的padding，最多2个

					for (let i = 0; i < paddingCount; i++) {
						processedData.categories.push('');
						processedData.series.forEach(serie => {
							serie.data.push(0);
						});
					}

					console.log(`添加${paddingCount}个空白数据点，确保最后的数据能完整显示`);
				} else if (dataLength === itemCount) {
					// 如果数据长度正好等于itemCount，添加1个空白数据点确保边界显示
					processedData.categories.push('');
					processedData.series.forEach(serie => {
						serie.data.push(0);
					});

					console.log('数据长度等于itemCount，添加1个空白数据点确保边界显示');
				}

				console.log('处理后的categories:', processedData.categories);
				console.log('处理后的数据长度:', processedData.categories.length);

				return processedData;
			},
			touchstart(e) {
				if (uChartsInstance[e.target.id]) {
					uChartsInstance[e.target.id].scrollStart(e);
				}
			},
			touchmove(e) {
				// 节流处理，减少触摸移动事件的处理频率
				if (this.touchMoveTimer) {
					clearTimeout(this.touchMoveTimer);
				}
				this.touchMoveTimer = setTimeout(() => {
					if (uChartsInstance[e.target.id]) {
						uChartsInstance[e.target.id].scroll(e);
					}
				}, 16); // 约60fps
			},
			touchend(e) {
				if (this.touchMoveTimer) {
					clearTimeout(this.touchMoveTimer);
					this.touchMoveTimer = null;
				}
				if (uChartsInstance[e.target.id]) {
					uChartsInstance[e.target.id].scrollEnd(e);
					// 移除不必要的事件处理，减少性能消耗
					// uChartsInstance[e.target.id].touchLegend(e);
					// uChartsInstance[e.target.id].showToolTip(e);
				}
			},
			saveQRCode() {
				uni.saveImageToPhotosAlbum({
					filePath: '../../static/qrcode.jpg',
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					}
				});
			},
			treeJ() {
				if (this.tree1 == 4) {
					this.tree1 = 1
				} else {
					this.tree1++
				}
			},
			goAnswer() {
				uni.navigateTo({
					url: '/pages/answer/start'
				})
			},
			sk() {
				if(!this.notvip){
					uni.redirectTo({
										url: '/pages/answer/learn'
									})
				}else{
					this.qrCodeVisible = true;
				}
				
			},

			pgt() {
				if(!this.notvip){
					uni.redirectTo({
										url: '/pages/answer/see'
									})
				}else{
					this.qrCodeVisible = true;
				}
			 
			},
			gokm(id,score) {
			 
				uni.navigateTo({
					url: '/pages/index/km?id='+id+"&score="+score,
				});
			},
			getProgress(score) {
				// 将分数转换为百分比（0-100）
				const value = parseFloat(score);
				return value + '%';
			}
		},
	}
</script>

<style lang="scss" scoped>
	// 横向滑动
	.scroll-view {
		white-space: nowrap;
		width: 100%;
		padding: 10rpx;

		.item {
			width: 250rpx;
			height: 340rpx;
			display: inline-flex;
			border-radius: 20rpx;
			margin: 12rpx 15rpx 30rpx 15rpx;

			.ucharsItem {
				width: 250rpx;
				height: 300rpx;

				.charts_nav {
					width: 210rpx;
					height: 245rpx;
				}
			}
		}
	}

	.chartBox1 {
		width: 700rpx;
		
		padding: 25rpx;
		margin: 0 auto;
		border-radius: 20rpx;
	}

	.zthH {
		height: var(--status-bar-height);
		margin-top: 30rpx;
		width: 100%;
	}

	.bgBox {
		width: 750rpx;
	 
		background-image: linear-gradient(to top, rgba(242, 245, 249, 1) 0%, rgba(255, 255, 255, 0) 6%), url(/static/bg2.jpg);
		background-position: left top;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		position: relative;
		padding: 30rpx;

		.topNav {
			height: 260rpx;
			 
			padding: 12rpx 20rpx;
			border-radius: 30rpx;
			background-color: rgba(255, 255, 255, 0.3);
			box-sizing: border-box;
			overflow: hidden;
		}

		.topNav2 {
			width: 45%;
			height: 120rpx;
			padding: 10rpx 6rpx 10rpx 20rpx;
			border-radius: 30rpx;
			background-color: rgba(255, 255, 255, 0.3);
			box-sizing: border-box;
		}

		.tree_img {
			position: absolute;
			width: 500rpx;
			height: 580rpx;
			left: calc(50% - 270rpx);
			bottom: 200rpx;
			background-position: bottom;
			background-repeat: no-repeat;
			background-size: 500rpx;
		}

	 
	}

	.bottomBox {
		width: 680rpx;
		border-radius: 18rpx;
		margin: 0rpx auto;

		.title-header {
			display: flex;
			height: 120rpx;
			font-size: 38rpx;
			align-items: center;
			justify-content: center;
			/* padding: 40rpx 0 0 0; */
			font-weight: bold;
			background-image: url(@/static/icon/wccswF.png);
			background-size: cover;
		}

		.title-text {
			background-image: -webkit-linear-gradient(0deg, #ff8a34, #FBBD12);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			/* border:10px solid #ddd;
		  border-image: -webkit-linear-gradient(red,yellow);
		 	border-image: -moz-linear-gradient(red,yellow);
		  border-image: linear-gradient(red,yellow);  */
		}
	}

	// .icon_treeChange{
	// 	width: 150rpx;
	// 	position: absolute;
	// 	right: 10rpx;
	// 	bottom: 10rpx;
	// }

	@font-face {
		font-family: 'z1';
		src: url('/static/z1.woff2') format('woff2');
		;
		font-display: fallback;
		font-weight: normal;
	}

	.progress-circle {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background: conic-gradient(#2fc25b 0% calc(var(--progress)), #f0f0f0 calc(var(--progress)) 100%);
		margin: 20rpx auto;
		position: relative;
		box-shadow: 0 0 10rpx rgba(47, 194, 91, 0.2);
		
		&::after {
			content: '';
			position: absolute;
			top: -4rpx;
			left: -4rpx;
			right: -4rpx;
			bottom: -4rpx;
			background: linear-gradient(135deg, rgba(47, 194, 91, 0.5), transparent 50%);
			border-radius: 50%;
			z-index: -1;
		}
		
		&::before {
			content: '';
			position: absolute;
			width: 130rpx;
			height: 130rpx;
			background: white;
			border-radius: 50%;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
		
		.progress-circle-inner {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			text-align: center;
			
			.score {
				font-size: 40rpx;
				font-weight: bold;
				color: #2fc25b;
				display: block;
			}
			
			.unit {
				font-size: 20rpx;
				color: #999;
			}
		}
	}

	.stat-box {
		background-color: rgba(255, 255, 255, 0.3);
		padding: 20rpx 40rpx;
		border-radius: 30rpx;
		text-align: center;
		width: 45%;
		
		.stat-value {
			font-size: 36rpx;
			color: #2fc25b;
			margin-bottom: 6rpx;
		}
		
		.stat-label {
			font-size: 24rpx;
			color: #666;
		}
	}

	.badge {
		background-color: #ff4d4f;
		color: white;
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		border-radius: 20rpx;
		position: absolute;
		top: -10rpx;
		right: -24rpx;
		min-width: 32rpx;
		text-align: center;
	}

	.text-bold {
		position: relative;
		display: inline-block;
	}
	
	.qr-popup {
		background: #fff;
		width: 600rpx;
		border-radius: 12rpx;
		
		.qr-code {
			width: 400rpx;
			height: 400rpx;
			display: block;
			margin: 0 auto;
		
		}
		}


.stats-header {
  display: flex;
  text-align: left;
  justify-content:flex-start;
  padding: 0rpx 10rpx;
  margin: 0rpx;
}

.stat-item {
	text-align: left;
  display: flex;
  
 margin-right: 20px;
  align-items: left;
}

.stat-label {
  font-size: 24rpx;
  color: #686565;
  margin-right: 10rpx;
}

.stat-number {
  font-size: 33rpx;
  font-weight: bold;
}

.green {
  color: #4CD964;
}

.blue {
  color: #4CD964;
}


/* 广告位样式 */
.ad-container {
  margin-top: 20rpx;
    margin-bottom: 20rpx;
}

.ad-swiper {
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
  color: white;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

/* 有效期显示样式 */
.expire-info {
  margin-top: 15rpx;
}

.expire-container {
  background: linear-gradient(135deg, #f8fffe 0%, #eef7f6 100%);
  border: 1rpx solid #d1ede8;
  border-radius: 20rpx;
  padding: 4rpx 6rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(47, 194, 91, 0.08);
}

.expire-icon {
 
  background: linear-gradient(135deg, #2fc25b, #26b549);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(47, 194, 91, 0.2);
}

.expire-icon .cuIcon-time {
  color: white;
  font-size: 26rpx;
  font-weight: bold;
}

.expire-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.expire-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 2rpx;
  line-height: 1.2;
}

.expire-date {
  font-size: 18rpx;
  font-weight: bold;
  color: #2fc25b;
  font-family: 'PingFang SC', -apple-system, sans-serif;
  line-height: 1.2;
  
  &.expired {
    color: #ff4757; // 过期时显示红色
  }
}
	
</style>



