<template>
	<view>
		<!-- 自定义顶部 -->
		<cu-custom bgColor="bg-yellow2" :isBack="false" :isLogo="false">
				<block class="text-bold text-lg" slot="content">练习</block>
		</cu-custom>

		<view class="carBox">
			<view class="cu-form-group">



				<view class="title text-bold text-black">学科</view>
				<u-tabs :list="biglesson" :is-scroll="true" :current="current" @change="click"></u-tabs>


			</view>

			<!-- 
			<view class="cu-form-group">



				<view class="title text-bold text-black">学科</view>
				<picker @change="getlessall" :value="index_race" :range="picker_race">
					<view class="picker" style="color: #555;">
						{{index_race>-1?picker_race[index_race]:'请选择'}}
					</view>
				</picker>
			</view>
-->
			<view>
				<wyh-tree-select :items="items" @clickItem="onItem" :max="1" :activeIds="ids" @clickNav="onItem2"
					@onChange="bindChange($event, 'city_ids2')" />
			</view>




			<view class="padding flex justify-between margin-top-xs">
				<button @click="zsd()" class="cu-btn bg-yellow2 lg" style="width: 48%;">学习知识点</button>
				<button @click="start()" class="cu-btn bg-yellow2 lg" style="width: 48%;">开始练习</button>
			</view>

		</view>



		<view style="height: 190rpx;width: 1rpx;"></view>

		<u-popup v-model="qrCodeVisible" mode="center">
			<view class="qr-popup padding-lg">
				<view class="text-center padding-bottom">
					<text class="text-lg text-bold">到期提醒</text>
				</view>
				 
				<view class="text-center padding-top-sm">
					<text class="text-df">本科目会员过期，成绩为暂存状态，只显示选择题，“讲解”功能不能使用，为了不影响您的使用，请到公众号充值会员开通</text>
				</view>
			 
			</view>
		</u-popup>

		<Footer :footState="2" :num="messageCount"></Footer>

	</view>
</template>
<script>
	import request from '@/utils/request.js';
	import Footer from "@/components/footer/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				messageCount:0,
				ids: [],
				current: 0,
				biglesson: [],
				items: [],
				index_race: -1,
				picker_race: [],
				qrCodeVisible:false,

			}
		},
		async mounted() {
			await this.initdata()
		},



		async onShow() {
			this.current=0;
			await this.initdata()
			let res = await request.post('bk/count', {});
			this.messageCount=res.data.count;
		},
		methods: {
			async initdata() {
			//	let res = await request.post('lesson/getlesson', {});

  let res3=await request.post('index/lessonscore', {});


			 //res3 复制到res    lessonname 改为name  lessonid 改为id ，enddate 保留
			 let res = res3.map(item => ({
					name: item.lessonname,
					id: item.lessonid,
					enddate: item.enddate
				}))



				this.items = await request.post('lesson/getlessonall', {
					"id": res[0].id
				});

			if (!uni.getStorageSync('hasShownExpiredTip2')) {
				
				if(res[0]["enddate"]=='已过期'){
					this.qrCodeVisible = true;

					uni.setStorageSync('hasShownExpiredTip2', true);
				}
				
			}

				
				this.biglesson = res;

			},
			async click(index) {
				let obj = this.biglesson[index];

			// if(obj.enddate=='已过期'){
			// 		this.qrCodeVisible = true;
					 

			// 	}
				
				
				if (!uni.getStorageSync('hasShownExpiredTip2')) {
					
					if(obj.enddate=='已过期'){
						this.qrCodeVisible = true;
						uni.setStorageSync('hasShownExpiredTip2', true);
					
					}
					
				}


				this.items = await request.post('lesson/getlessonall', {
					"id": obj.id
				});
				console.log(this.items)
				this.current = index;
			},

			async getlessall(e) {
				this.ids = [];
				this.index_race = e.detail.value;
				console.log(this.biglesson[this.index_race])
				this.items = await request.post('lesson/getlessonall', {
					"id": this.biglesson[this.index_race]['id']
				});
				console.log(this.items);
			},
			onItem2() {
				this.ids = [];
			},
			onItem(ret) {
				console.log(ret);
			},
			zsd() {
				
				
				if (this.ids.length > 0) {
				
					uni.navigateTo({
						url: '/pages/answer/zsd?id=' + this.ids.join(",")
					})
				} else {
				
				
					uni.showToast({
						title: '请选择章节',
						icon: 'none'
					});
					return;
				}
				
				
			},
			start() {
					let obj = this.biglesson[this.current];
					let flag=0;
					if(obj.enddate=='已过期'){
						flag=1;
					}

				if (this.ids.length > 0) {

					uni.navigateTo({
						url: '/pages/answer/start?notvip='+flag+'&lesson=' + this.ids.join(",")
					})
				} else {


					uni.showToast({
						title: '请选择章节',
						icon: 'none'
					});
					return;
				}





			},
			bindChange(ids, key, popupRef) {
				this[key] = ids;
				if (popupRef) {
					this.$refs[popupRef].close();
				}
			},


		}
	}
</script>

<style lang="scss" scoped>
	/* 绘制图片canvas样式 */
	.canvas-poster {
		position: fixed;
		width: 500px;
		top: 100%;
		left: 100%;
	}

		
	.qr-popup {
		background: #fff;
		width: 600rpx;
		border-radius: 12rpx;
		
		.qr-code {
			width: 400rpx;
			height: 400rpx;
			display: block;
			margin: 0 auto;
		
		}
		}


	.filter {
		width: 100%;
		height: 600rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;

		.menu-box {
			width: 698rpx;
			height: calc(690rpx - 120rpx);
			flex-shrink: 1;

			.box {
				width: 100%;
				padding-top: 32rpx;
				flex-direction: column;
			}
		}
	}
</style>