{"code": 0, "data": {"id": "HnA19n", "name": "示例问卷", "mode": "survey", "status": 1, "survey": {"id": "HnA19n", "title": "示例问卷", "description": "感谢您能抽出几分钟时间来参加本次答题，现在我们就马上开始吧！", "type": "Survey", "attribute": {"suffix": "您已完成本次问卷，感谢您的帮助与支持", "submitButton": "提交", "globalLogic": [], "mode": "survey"}, "children": [{"id": "a8b1", "title": "<img src=\"/admin-api/infra/file/2/get/b1d47323ef073dd409a1dbf567344d1ff3f073bb7129cf363cec11a072ee86ce.png\">", "type": "Radio", "Analysis": "各大文化系统分处各地，有着土著的起源和自己的特色。v各大文化系统分处各地，有着土著的起源和自己的特色。各大文化系统分处各地，有着土著的起源和自己的特色。", "attribute": {"examScore": 3, "examAnswerMode": "onlyOne"}, "children": [{"id": "xi12", "title": "各大文化系统分处各地，有着土著的起源和自己的特色。", "type": "Option", "attribute": {}}, {"id": "cbt8", "title": "各文化系统之间的交流形成了中华民族格局中多元的起点。", "type": "Option", "attribute": {"examScore": 3, "examCorrectAnswer": "cbt8"}}, {"id": "v7ao", "title": "黄河中下游的文化系统之间的相互结合开启了华夏文化。", "type": "Option", "attribute": {}}, {"id": "w9lu", "title": "各文化系统相互影响形成大的相互作用圈，逐渐走向一体。", "type": "Option", "attribute": {}}]}, {"id": "uvmw", "title": "解答题测试：一元二次方程<img src=\"/admin-api/infra/file/2/get/e36fadaeac6d023999cbab44bd2b05bc843cf82c7fc5dff24114b4a488ad7572.png\" width=\"109\">的一次项和常数项分别是", "type": "FillBlank", "attribute": {"required": true, "examScore": 3, "examAnswerMode": "onlyOne"}, "children": [{"id": "o5jo", "attribute": {}}]}, {"id": "g6cy", "title": "<img src=\"/admin-api/infra/file/2/get/886778c4be0ef28c0a22e192ef5703f9c12ce2815a243cb316655e5085f515ad.png\">", "type": "Upload", "attribute": {"required": true, "examScore": 3}, "children": [{"id": "te6a", "attribute": {}}]}, {"id": "kjsd", "title": "单选测试题：《九章算术》是我国古代数学的经典著作，书中有一个问题：“今有黄金九枚，白银一十一枚，称之重适等．交易其一，金轻十三两．问金、银一枚各重几何？”．意思是：今有甲种袋子中装有黄金9枚（每枚黄金重量相同），乙种袋子中装有白银11枚（每枚白银重量相同），称重两袋相等．两袋互相交换1枚后，甲种袋子比乙种袋子轻了13两（袋子重量忽略不计）．问黄金、白银每枚各重多少两？设每枚黄金重x两，每枚白银重y两，则可建立方程为（　　）", "type": "Radio", "attribute": {"required": true, "columns": 2, "examScore": 3, "examAnswerMode": "onlyOne"}, "children": [{"id": "vvhn", "title": "<img src=\"/admin-api/infra/file/2/get/8f7d80dc797d31c456167c698b415b21a33ab7d8ad21fbcd407fc5288106866a.png\">", "attribute": {}}, {"id": "ycvb", "title": "<img src=\"/admin-api/infra/file/2/get/3b3e7d706966b399715f6ed79bf786748dc5d219fced0e9b29f52c5c407be69e.png\">", "attribute": {}}, {"id": "poul", "title": "<img src=\"/admin-api/infra/file/2/get/5c5600acbca23f7932da5e66484cd7e44893df77c6b070b9ea93f41c3b1b4154.png\">", "attribute": {"examScore": 3, "examCorrectAnswer": "poul"}}, {"id": "pwlh", "title": "<img src=\"/admin-api/infra/file/2/get/cda774ee75ec66915b1b7cf4f3df3677e5ca4a760d4511615f32b26bc3e95602.png\">", "attribute": {}}], "Analysis": "三线合一+构造中位线|①GH⊥BC,因为∠1=∠2,根据等腰三角形三线合一得到H为BC中点,且∠3=∠4; |②∠H=∠BEC=∠A+∠3；∠HFC=∠4+∠3=∠1+∠2+∠3=∠A+∠3；| ③∠H=∠HFC，所以CH=CF=BE；"}, {"id": "l9pk", "title": "单选题", "type": "Radio", "attribute": {"required": true, "columns": 2, "examScore": 3}, "children": [{"id": "skwz", "title": "选项1", "attribute": {"examScore": 3}}, {"id": "oxjb", "title": "选项2", "attribute": {"examScore": 0}}], "Analysis": "三线合一+构造中位线|①GH⊥BC,因为∠1=∠2,根据等腰三角形三线合一得到H为BC中点,且∠3=∠4; |②∠H=∠BEC=∠A+∠3；∠HFC=∠4+∠3=∠1+∠2+∠3=∠A+∠3；| ③∠H=∠HFC，所以CH=CF=BE；"}, {"id": "dndf", "title": "<p>如图，在平面直角坐标系中，矩形<img src=\"/admin-api/infra/file/2/get/8a88f1d8b6fe65e86d743da42609a04b844e14970162b1ca9c1b8520d3e07846.png\">的对角线<img src=\"/admin-api/infra/file/2/get/7371fc93710a8e2b2f46216b7352b30ad15dcc61060e99591adcb277020dc8e3.png\">、<img src=\"/admin-api/infra/file/2/get/7662c13dbb3724e9c1a1779f2c331967557af2c36d5221c62edea8c0033391cf.png\">相交于点D，且<img src=\"/admin-api/infra/file/2/get/e8375cc25eabbf7700fb1f1272d3b8d9fde006d2a064293dc67d972de785b0c2.png\">，<img src=\"/admin-api/infra/file/2/get/0bb7f3641e8c717edbc2f45a25123369579979eb3c09bb7311dc3303269786ff.png\">，反比例函数<img src=\"/admin-api/infra/file/2/get/7a40bb00592158d2c6740ac3fb53a16488f7ea0dbfdb222705e91fc0edd8d7b4.png\">的图像经过点E，若 <img src=\"/admin-api/infra/file/2/get/d2959fc364638e173622c42bc674c0485de41078a94a8885ef58adfddf12094e.png\">，<img src=\"/admin-api/infra/file/2/get/850b6d4e3dc11dd8298c134ebc99faf87b35089679edf702ab986402789fea06.png\">，则k的值是（　　）</p><p><img src=\"/admin-api/infra/file/2/get/c877927d9ad1b7824a3907029aa0893cc229071b4d74eadc090ed82f0ea62684.png\" width=\"199\"></p>", "type": "Checkbox", "attribute": {"required": true, "examScore": 3}, "children": [{"id": "qx1k", "title": "选项1", "attribute": {"examScore": 1}}, {"id": "cera", "title": "选项2", "attribute": {"examScore": 1}}, {"id": "fq64", "title": "选项3", "attribute": {}}, {"id": "fqj6", "title": "选项4", "attribute": {}}], "Analysis": "答案解析内容；"}]}, "setting": {"status": 1, "answerSetting": {"progressBar": true, "questionNumber": true, "autoSave": true, "triggerType": "onBlur", "copyEnabled": true, "qrCodeVisible": true, "anonymousAnswer": false, "footerVisible": true, "headerVisible": true}, "submittedSetting": {"publicQuery": [{"id": "GDjL4Z", "enabled": false}]}, "examSetting": {}, "wechatSetting": {}, "dimensionSetting": {}, "appraiseSetting": {}}, "createTime": "2023-02-11 17:20", "updateTime": "2024-03-30 01:45", "parentId": "0", "creator": "1", "partner": {"type": 1}}, "msg": ""}